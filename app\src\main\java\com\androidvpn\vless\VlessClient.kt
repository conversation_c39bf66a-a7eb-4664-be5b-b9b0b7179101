package com.androidvpn.vless

import com.androidvpn.model.VlessConfig
import kotlinx.coroutines.*
import okhttp3.*
import okhttp3.internal.closeQuietly
import java.io.*
import java.net.*
import java.nio.ByteBuffer
import java.security.SecureRandom
import java.util.*
import java.util.concurrent.TimeUnit
import javax.net.ssl.*

/**
 * VLESS protocol client implementation
 */
class VlessClient(private val config: VlessConfig) {
    
    companion object {
        private const val VLESS_VERSION = 0
        private const val COMMAND_TCP = 1
        private const val COMMAND_UDP = 2
        private const val ADDRESS_TYPE_IPV4 = 1
        private const val ADDRESS_TYPE_DOMAIN = 2
        private const val ADDRESS_TYPE_IPV6 = 3
        
        private const val CONNECT_TIMEOUT = 10000L
        private const val READ_TIMEOUT = 30000L
    }
    
    private var socket: Socket? = null
    private var webSocket: WebSocket? = null
    private var inputStream: InputStream? = null
    private var outputStream: OutputStream? = null
    private var isConnected = false
    
    private val random = SecureRandom()
    
    suspend fun connect(): Boolean = withContext(Dispatchers.IO) {
        try {
            when (config.network) {
                "tcp" -> connectTcp()
                "ws" -> connectWebSocket()
                "grpc" -> connectGrpc()
                "h2" -> connectHttp2()
                else -> false
            }
        } catch (e: Exception) {
            false
        }
    }
    
    private suspend fun connectTcp(): Boolean = withContext(Dispatchers.IO) {
        try {
            val socketAddress = InetSocketAddress(config.address, config.port)
            
            socket = if (config.security == "tls") {
                createTlsSocket(socketAddress)
            } else {
                Socket().apply {
                    connect(socketAddress, CONNECT_TIMEOUT.toInt())
                    soTimeout = READ_TIMEOUT.toInt()
                }
            }
            
            inputStream = socket?.getInputStream()
            outputStream = socket?.getOutputStream()
            
            // Send VLESS handshake
            sendVlessHandshake()
            
            isConnected = true
            true
        } catch (e: Exception) {
            cleanup()
            false
        }
    }
    
    private suspend fun connectWebSocket(): Boolean = withContext(Dispatchers.IO) {
        try {
            val client = createOkHttpClient()
            val url = buildWebSocketUrl()
            
            val request = Request.Builder()
                .url(url)
                .apply {
                    config.host?.let { addHeader("Host", it) }
                }
                .build()
            
            val latch = CompletableDeferred<Boolean>()
            
            webSocket = client.newWebSocket(request, object : WebSocketListener() {
                override fun onOpen(webSocket: WebSocket, response: Response) {
                    latch.complete(true)
                }
                
                override fun onFailure(webSocket: WebSocket, t: Throwable, response: Response?) {
                    latch.complete(false)
                }
                
                override fun onMessage(webSocket: WebSocket, bytes: ByteString) {
                    // Handle incoming data
                    handleWebSocketMessage(bytes.toByteArray())
                }
            })
            
            val connected = latch.await()
            if (connected) {
                // Send VLESS handshake through WebSocket
                sendVlessHandshakeWs()
                isConnected = true
            }
            
            connected
        } catch (e: Exception) {
            cleanup()
            false
        }
    }
    
    private fun createTlsSocket(address: InetSocketAddress): Socket {
        val context = SSLContext.getInstance("TLS")
        context.init(null, arrayOf(createTrustAllManager()), random)
        
        val factory = context.socketFactory
        val sslSocket = factory.createSocket() as SSLSocket
        
        // Configure TLS parameters
        config.alpn?.let { alpn ->
            try {
                val setApplicationProtocols = sslSocket.javaClass.getMethod(
                    "setApplicationProtocols", Array<String>::class.java
                )
                setApplicationProtocols.invoke(sslSocket, alpn.toTypedArray())
            } catch (e: Exception) {
                // Ignore if ALPN is not supported
            }
        }
        
        // Set SNI
        config.sni?.let { sni ->
            val sslParams = sslSocket.sslParameters
            sslParams.serverNames = listOf(SNIHostName(sni))
            sslSocket.sslParameters = sslParams
        }
        
        sslSocket.connect(address, CONNECT_TIMEOUT.toInt())
        sslSocket.soTimeout = READ_TIMEOUT.toInt()
        sslSocket.startHandshake()
        
        return sslSocket
    }
    
    private fun createTrustAllManager(): X509TrustManager {
        return object : X509TrustManager {
            override fun checkClientTrusted(chain: Array<java.security.cert.X509Certificate>, authType: String) {}
            override fun checkServerTrusted(chain: Array<java.security.cert.X509Certificate>, authType: String) {}
            override fun getAcceptedIssuers(): Array<java.security.cert.X509Certificate> = arrayOf()
        }
    }
    
    private fun createOkHttpClient(): OkHttpClient {
        val builder = OkHttpClient.Builder()
            .connectTimeout(CONNECT_TIMEOUT, TimeUnit.MILLISECONDS)
            .readTimeout(READ_TIMEOUT, TimeUnit.MILLISECONDS)
            .writeTimeout(READ_TIMEOUT, TimeUnit.MILLISECONDS)
        
        if (config.security == "tls") {
            val trustManager = createTrustAllManager()
            val sslContext = SSLContext.getInstance("TLS")
            sslContext.init(null, arrayOf(trustManager), random)
            
            builder.sslSocketFactory(sslContext.socketFactory, trustManager)
            
            if (config.allowInsecure) {
                builder.hostnameVerifier { _, _ -> true }
            }
        }
        
        return builder.build()
    }
    
    private fun buildWebSocketUrl(): String {
        val scheme = if (config.security == "tls") "wss" else "ws"
        val path = config.path ?: "/"
        return "$scheme://${config.address}:${config.port}$path"
    }
    
    private suspend fun connectGrpc(): Boolean {
        // Simplified gRPC implementation
        // In a real implementation, you would use a proper gRPC client
        return connectTcp()
    }
    
    private suspend fun connectHttp2(): Boolean {
        // Simplified HTTP/2 implementation
        // In a real implementation, you would use HTTP/2 client
        return connectWebSocket()
    }
    
    private suspend fun sendVlessHandshake() {
        val handshake = buildVlessHandshake()
        outputStream?.write(handshake)
        outputStream?.flush()
        
        // Read response (simplified)
        val response = ByteArray(1)
        inputStream?.read(response)
    }
    
    private suspend fun sendVlessHandshakeWs() {
        val handshake = buildVlessHandshake()
        webSocket?.send(ByteString.of(*handshake))
    }
    
    private fun buildVlessHandshake(): ByteArray {
        val buffer = ByteArrayOutputStream()
        
        // VLESS version
        buffer.write(VLESS_VERSION)
        
        // UUID (16 bytes)
        val uuid = UUID.fromString(config.uuid)
        val uuidBytes = ByteBuffer.allocate(16)
            .putLong(uuid.mostSignificantBits)
            .putLong(uuid.leastSignificantBits)
            .array()
        buffer.write(uuidBytes)
        
        // Additional info length (1 byte)
        buffer.write(0)
        
        // Command (1 byte) - TCP
        buffer.write(COMMAND_TCP)
        
        // Port (2 bytes)
        buffer.write((config.port shr 8) and 0xFF)
        buffer.write(config.port and 0xFF)
        
        // Address type and address
        val addressBytes = config.address.toByteArray()
        if (isIpAddress(config.address)) {
            buffer.write(ADDRESS_TYPE_IPV4)
            buffer.write(addressBytes)
        } else {
            buffer.write(ADDRESS_TYPE_DOMAIN)
            buffer.write(addressBytes.size)
            buffer.write(addressBytes)
        }
        
        return buffer.toByteArray()
    }
    
    private fun isIpAddress(address: String): Boolean {
        return try {
            InetAddress.getByName(address) is Inet4Address
        } catch (e: Exception) {
            false
        }
    }
    
    suspend fun sendData(data: ByteArray, length: Int) {
        if (!isConnected) return
        
        try {
            when (config.network) {
                "ws" -> {
                    webSocket?.send(ByteString.of(*data, 0, length))
                }
                else -> {
                    outputStream?.write(data, 0, length)
                    outputStream?.flush()
                }
            }
        } catch (e: Exception) {
            isConnected = false
            throw e
        }
    }
    
    suspend fun receiveData(): ByteArray? {
        if (!isConnected) return null
        
        return try {
            when (config.network) {
                "ws" -> {
                    // For WebSocket, data is received in the listener
                    delay(10)
                    null
                }
                else -> {
                    val buffer = ByteArray(32767)
                    val length = inputStream?.read(buffer) ?: -1
                    if (length > 0) {
                        buffer.copyOf(length)
                    } else {
                        null
                    }
                }
            }
        } catch (e: Exception) {
            isConnected = false
            null
        }
    }
    
    private fun handleWebSocketMessage(data: ByteArray) {
        // Handle incoming WebSocket data
        // This would typically be queued for the VPN service to read
    }
    
    suspend fun ping(): Int {
        return try {
            val startTime = System.currentTimeMillis()
            // Send a small ping packet
            sendData(byteArrayOf(0), 1)
            val endTime = System.currentTimeMillis()
            (endTime - startTime).toInt()
        } catch (e: Exception) {
            -1
        }
    }
    
    fun close() {
        isConnected = false
        cleanup()
    }
    
    private fun cleanup() {
        webSocket?.close(1000, "Closing")
        webSocket = null
        
        inputStream?.closeQuietly()
        outputStream?.closeQuietly()
        socket?.closeQuietly()
        
        inputStream = null
        outputStream = null
        socket = null
    }
}
