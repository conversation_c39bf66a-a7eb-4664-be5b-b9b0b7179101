package com.androidvpn.service

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Intent
import android.net.VpnService
import android.os.Binder
import android.os.Build
import android.os.IBinder
import android.os.ParcelFileDescriptor
import androidx.core.app.NotificationCompat
import com.androidvpn.R
import com.androidvpn.model.ConnectionState
import com.androidvpn.model.ConnectionStats
import com.androidvpn.model.VlessConfig
import com.androidvpn.model.VpnConnectionInfo
import com.androidvpn.ui.MainActivity
import com.androidvpn.utils.StatisticsMonitor
import com.androidvpn.vless.VlessClient
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import java.io.FileInputStream
import java.io.FileOutputStream
import java.nio.ByteBuffer
import java.util.concurrent.atomic.AtomicLong

class VpnService : VpnService() {
    
    companion object {
        private const val NOTIFICATION_ID = 1001
        private const val CHANNEL_ID = "VPN_SERVICE_CHANNEL"
        private const val ACTION_DISCONNECT = "com.androidvpn.DISCONNECT"
        
        const val EXTRA_CONFIG = "extra_config"
    }
    
    private val binder = VpnServiceBinder()
    private var vpnInterface: ParcelFileDescriptor? = null
    private var vlessClient: VlessClient? = null
    private var serviceScope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    private var statisticsMonitor: StatisticsMonitor? = null
    
    // Connection state
    private val _connectionInfo = MutableStateFlow(VpnConnectionInfo())
    val connectionInfo: StateFlow<VpnConnectionInfo> = _connectionInfo
    
    // Statistics tracking
    private val uploadBytes = AtomicLong(0)
    private val downloadBytes = AtomicLong(0)
    private var connectionStartTime: Long = 0
    private var statsJob: Job? = null
    
    inner class VpnServiceBinder : Binder() {
        fun getService(): VpnService = this@VpnService
    }
    
    override fun onBind(intent: Intent?): IBinder = binder
    
    override fun onCreate() {
        super.onCreate()
        createNotificationChannel()
    }
    
    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        when (intent?.action) {
            ACTION_DISCONNECT -> {
                disconnect()
                return START_NOT_STICKY
            }
            else -> {
                val config = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                    intent?.getParcelableExtra(EXTRA_CONFIG, VlessConfig::class.java)
                } else {
                    @Suppress("DEPRECATION")
                    intent?.getParcelableExtra(EXTRA_CONFIG)
                }
                
                config?.let { connect(it) }
            }
        }
        
        return START_STICKY
    }
    
    override fun onDestroy() {
        super.onDestroy()
        disconnect()
        serviceScope.cancel()
    }
    
    fun connect(config: VlessConfig) {
        if (_connectionInfo.value.state == ConnectionState.CONNECTING ||
            _connectionInfo.value.state == ConnectionState.CONNECTED) {
            return
        }
        
        serviceScope.launch {
            try {
                updateConnectionState(ConnectionState.CONNECTING, config)
                
                // Create VPN interface
                val builder = Builder()
                    .setSession(config.name)
                    .addAddress("********", 24)
                    .addDnsServer("*******")
                    .addDnsServer("*******")
                    .addRoute("0.0.0.0", 0)
                    .setMtu(1500)
                
                vpnInterface = builder.establish()
                
                if (vpnInterface == null) {
                    updateConnectionState(ConnectionState.ERROR, config, "Failed to establish VPN interface")
                    return@launch
                }
                
                // Initialize VLESS client
                vlessClient = VlessClient(config)
                
                // Start VPN tunnel
                startVpnTunnel()
                
                // Update state to connected
                connectionStartTime = System.currentTimeMillis()
                updateConnectionState(ConnectionState.CONNECTED, config)
                
                // Start statistics monitoring
                statisticsMonitor = StatisticsMonitor(this@VpnService)
                statisticsMonitor?.startMonitoring(config.address, config.port)
                startStatsTracking()
                
                // Show notification
                startForeground(NOTIFICATION_ID, createNotification(config))
                
            } catch (e: Exception) {
                updateConnectionState(ConnectionState.ERROR, config, e.message)
            }
        }
    }
    
    fun disconnect() {
        serviceScope.launch {
            updateConnectionState(ConnectionState.DISCONNECTING)
            
            // Stop statistics monitoring
            statisticsMonitor?.stopMonitoring()
            statisticsMonitor = null

            // Stop statistics tracking
            statsJob?.cancel()
            
            // Close VPN interface
            vpnInterface?.close()
            vpnInterface = null
            
            // Close VLESS client
            vlessClient?.close()
            vlessClient = null
            
            // Reset statistics
            uploadBytes.set(0)
            downloadBytes.set(0)
            connectionStartTime = 0
            
            updateConnectionState(ConnectionState.DISCONNECTED)
            
            // Stop foreground service
            stopForeground(true)
            stopSelf()
        }
    }
    
    private suspend fun startVpnTunnel() {
        val vpnInput = FileInputStream(vpnInterface!!.fileDescriptor)
        val vpnOutput = FileOutputStream(vpnInterface!!.fileDescriptor)
        
        // Launch coroutines for handling VPN traffic
        serviceScope.launch {
            handleVpnToRemote(vpnInput)
        }
        
        serviceScope.launch {
            handleRemoteToVpn(vpnOutput)
        }
    }
    
    private suspend fun handleVpnToRemote(vpnInput: FileInputStream) {
        val buffer = ByteArray(32767)
        
        try {
            while (isActive && vpnInterface != null) {
                val length = vpnInput.read(buffer)
                if (length > 0) {
                    // Send data through VLESS client
                    vlessClient?.sendData(buffer, length)
                    uploadBytes.addAndGet(length.toLong())
                }
            }
        } catch (e: Exception) {
            if (isActive) {
                updateConnectionState(ConnectionState.ERROR, errorMessage = e.message)
            }
        }
    }
    
    private suspend fun handleRemoteToVpn(vpnOutput: FileOutputStream) {
        try {
            while (isActive && vpnInterface != null) {
                val data = vlessClient?.receiveData()
                data?.let {
                    vpnOutput.write(it)
                    downloadBytes.addAndGet(it.size.toLong())
                }
            }
        } catch (e: Exception) {
            if (isActive) {
                updateConnectionState(ConnectionState.ERROR, errorMessage = e.message)
            }
        }
    }
    
    private fun startStatsTracking() {
        statsJob = serviceScope.launch {
            while (isActive) {
                delay(1000) // Update every second

                // Get statistics from monitor
                val stats = statisticsMonitor?.getCurrentStats() ?: ConnectionStats()

                // Update connection info with latest stats
                updateConnectionStats(stats)
            }
        }
    }
    
    private suspend fun measurePing(): Int {
        return try {
            vlessClient?.ping() ?: 0
        } catch (e: Exception) {
            0
        }
    }
    
    private fun updateConnectionState(
        state: ConnectionState,
        config: VlessConfig? = null,
        errorMessage: String? = null
    ) {
        val currentInfo = _connectionInfo.value
        _connectionInfo.value = currentInfo.copy(
            state = state,
            config = config ?: currentInfo.config,
            errorMessage = errorMessage,
            connectedAt = if (state == ConnectionState.CONNECTED) System.currentTimeMillis() else currentInfo.connectedAt
        )
    }
    
    private fun updateConnectionStats(stats: ConnectionStats) {
        val currentInfo = _connectionInfo.value
        _connectionInfo.value = currentInfo.copy(stats = stats)
    }
    
    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                getString(R.string.vpn_channel_name),
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = getString(R.string.vpn_channel_description)
                setShowBadge(false)
            }
            
            val notificationManager = getSystemService(NotificationManager::class.java)
            notificationManager.createNotificationChannel(channel)
        }
    }
    
    private fun createNotification(config: VlessConfig): Notification {
        val mainIntent = Intent(this, MainActivity::class.java)
        val mainPendingIntent = PendingIntent.getActivity(
            this, 0, mainIntent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        
        val disconnectIntent = Intent(this, VpnService::class.java).apply {
            action = ACTION_DISCONNECT
        }
        val disconnectPendingIntent = PendingIntent.getService(
            this, 0, disconnectIntent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        
        return NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle(getString(R.string.vpn_notification_title))
            .setContentText("${getString(R.string.vpn_notification_text)} - ${config.name}")
            .setSmallIcon(R.drawable.ic_vpn_key)
            .setContentIntent(mainPendingIntent)
            .addAction(
                R.drawable.ic_close,
                getString(R.string.disconnect),
                disconnectPendingIntent
            )
            .setOngoing(true)
            .setCategory(NotificationCompat.CATEGORY_SERVICE)
            .build()
    }
}
