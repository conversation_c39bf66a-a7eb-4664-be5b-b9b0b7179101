<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="16dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- Real-time Speed Chart -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Real-time Speed"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_primary"
                    android:layout_marginBottom="12dp" />

                <com.github.mikephil.charting.charts.LineChart
                    android:id="@+id/chartRealTimeSpeed"
                    android:layout_width="match_parent"
                    android:layout_height="200dp" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- Current Speed Stats -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Current Speed"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_primary"
                    android:layout_marginBottom="12dp" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:weightSum="2">

                    <!-- Download Speed -->
                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical"
                        android:gravity="center">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@drawable/ic_download"
                            android:tint="@color/status_connected"
                            android:layout_marginBottom="4dp" />

                        <TextView
                            android:id="@+id/tvCurrentDownloadSpeed"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="0 KB/s"
                            android:textSize="16sp"
                            android:textStyle="bold"
                            android:textColor="@color/status_connected" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Download"
                            android:textSize="12sp"
                            android:textColor="@color/text_secondary" />

                    </LinearLayout>

                    <!-- Upload Speed -->
                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical"
                        android:gravity="center">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@drawable/ic_upload"
                            android:tint="@color/vpn_primary"
                            android:layout_marginBottom="4dp" />

                        <TextView
                            android:id="@+id/tvCurrentUploadSpeed"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="0 KB/s"
                            android:textSize="16sp"
                            android:textStyle="bold"
                            android:textColor="@color/vpn_primary" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Upload"
                            android:textSize="12sp"
                            android:textColor="@color/text_secondary" />

                    </LinearLayout>

                </LinearLayout>

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- Data Usage Stats -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Data Usage"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_primary"
                    android:layout_marginBottom="12dp" />

                <!-- Session Usage -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="8dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="This Session"
                        android:textColor="@color/text_primary" />

                    <TextView
                        android:id="@+id/tvSessionUsage"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="0 MB"
                        android:textStyle="bold"
                        android:textColor="@color/text_primary" />

                </LinearLayout>

                <!-- Today Usage -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="8dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Today"
                        android:textColor="@color/text_primary" />

                    <TextView
                        android:id="@+id/tvTodayUsage"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="0 MB"
                        android:textStyle="bold"
                        android:textColor="@color/text_primary" />

                </LinearLayout>

                <!-- Monthly Usage -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="This Month"
                        android:textColor="@color/text_primary" />

                    <TextView
                        android:id="@+id/tvMonthlyUsage"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="0 GB"
                        android:textStyle="bold"
                        android:textColor="@color/text_primary" />

                </LinearLayout>

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- Connection Quality -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Connection Quality"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_primary"
                    android:layout_marginBottom="12dp" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:weightSum="3">

                    <!-- Ping -->
                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical"
                        android:gravity="center">

                        <TextView
                            android:id="@+id/tvPingValue"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="--"
                            android:textSize="20sp"
                            android:textStyle="bold"
                            android:textColor="@color/text_primary" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Ping (ms)"
                            android:textSize="12sp"
                            android:textColor="@color/text_secondary" />

                    </LinearLayout>

                    <!-- Quality -->
                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical"
                        android:gravity="center">

                        <TextView
                            android:id="@+id/tvQualityValue"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="--"
                            android:textSize="16sp"
                            android:textStyle="bold"
                            android:textColor="@color/text_primary" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Quality"
                            android:textSize="12sp"
                            android:textColor="@color/text_secondary" />

                    </LinearLayout>

                    <!-- Uptime -->
                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical"
                        android:gravity="center">

                        <TextView
                            android:id="@+id/tvUptimeValue"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="--"
                            android:textSize="16sp"
                            android:textStyle="bold"
                            android:textColor="@color/text_primary" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Uptime"
                            android:textSize="12sp"
                            android:textColor="@color/text_secondary" />

                    </LinearLayout>

                </LinearLayout>

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

    </LinearLayout>

</ScrollView>
