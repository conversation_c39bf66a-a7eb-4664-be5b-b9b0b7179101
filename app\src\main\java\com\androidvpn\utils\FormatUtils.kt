package com.androidvpn.utils

import java.text.DecimalFormat
import java.util.concurrent.TimeUnit

/**
 * Utility class for formatting various data types
 */
object FormatUtils {
    
    private val decimalFormat = DecimalFormat("#.##")
    
    /**
     * Format bytes to human readable format
     */
    fun formatBytes(bytes: Long): String {
        if (bytes < 0) return "0 B"
        
        val units = arrayOf("B", "KB", "MB", "GB", "TB")
        var size = bytes.toDouble()
        var unitIndex = 0
        
        while (size >= 1024 && unitIndex < units.size - 1) {
            size /= 1024
            unitIndex++
        }
        
        return "${decimalFormat.format(size)} ${units[unitIndex]}"
    }
    
    /**
     * Format speed (bytes per second) to human readable format
     */
    fun formatSpeed(bytesPerSecond: Long): String {
        if (bytesPerSecond < 0) return "0 B/s"
        
        val units = arrayOf("B/s", "KB/s", "MB/s", "GB/s")
        var speed = bytesPerSecond.toDouble()
        var unitIndex = 0
        
        while (speed >= 1024 && unitIndex < units.size - 1) {
            speed /= 1024
            unitIndex++
        }
        
        return "${decimalFormat.format(speed)} ${units[unitIndex]}"
    }
    
    /**
     * Format duration in milliseconds to human readable format
     */
    fun formatDuration(durationMs: Long): String {
        if (durationMs < 0) return "00:00:00"
        
        val hours = TimeUnit.MILLISECONDS.toHours(durationMs)
        val minutes = TimeUnit.MILLISECONDS.toMinutes(durationMs) % 60
        val seconds = TimeUnit.MILLISECONDS.toSeconds(durationMs) % 60
        
        return String.format("%02d:%02d:%02d", hours, minutes, seconds)
    }
    
    /**
     * Format duration in milliseconds to short format (e.g., "2h 30m")
     */
    fun formatDurationShort(durationMs: Long): String {
        if (durationMs < 0) return "0s"
        
        val hours = TimeUnit.MILLISECONDS.toHours(durationMs)
        val minutes = TimeUnit.MILLISECONDS.toMinutes(durationMs) % 60
        val seconds = TimeUnit.MILLISECONDS.toSeconds(durationMs) % 60
        
        return when {
            hours > 0 -> "${hours}h ${minutes}m"
            minutes > 0 -> "${minutes}m ${seconds}s"
            else -> "${seconds}s"
        }
    }
    
    /**
     * Format ping in milliseconds
     */
    fun formatPing(pingMs: Int): String {
        return when {
            pingMs < 0 -> "N/A"
            pingMs == 0 -> "0ms"
            else -> "${pingMs}ms"
        }
    }
    
    /**
     * Get signal strength description based on ping
     */
    fun getSignalStrengthDescription(pingMs: Int): String {
        return when {
            pingMs < 0 -> "Unknown"
            pingMs < 50 -> "Excellent"
            pingMs < 100 -> "Good"
            pingMs < 150 -> "Fair"
            pingMs < 300 -> "Poor"
            else -> "Very Poor"
        }
    }
    
    /**
     * Format percentage
     */
    fun formatPercentage(value: Double): String {
        return "${decimalFormat.format(value)}%"
    }
    
    /**
     * Format IP address with port
     */
    fun formatAddress(address: String, port: Int): String {
        return if (isIPv6(address)) {
            "[$address]:$port"
        } else {
            "$address:$port"
        }
    }
    
    /**
     * Check if address is IPv6
     */
    private fun isIPv6(address: String): Boolean {
        return address.contains(":")
    }
    
    /**
     * Truncate string to specified length with ellipsis
     */
    fun truncateString(text: String, maxLength: Int): String {
        return if (text.length <= maxLength) {
            text
        } else {
            "${text.substring(0, maxLength - 3)}..."
        }
    }
    
    /**
     * Format server name for display
     */
    fun formatServerName(name: String, maxLength: Int = 20): String {
        return truncateString(name, maxLength)
    }
    
    /**
     * Format network type for display
     */
    fun formatNetworkType(network: String): String {
        return when (network.lowercase()) {
            "tcp" -> "TCP"
            "ws" -> "WebSocket"
            "grpc" -> "gRPC"
            "h2" -> "HTTP/2"
            else -> network.uppercase()
        }
    }
    
    /**
     * Format security type for display
     */
    fun formatSecurityType(security: String): String {
        return when (security.lowercase()) {
            "none" -> "None"
            "tls" -> "TLS"
            "reality" -> "Reality"
            else -> security.uppercase()
        }
    }
    
    /**
     * Get color resource ID based on connection state
     */
    fun getConnectionStateColor(state: String): Int {
        return when (state.lowercase()) {
            "connected" -> android.R.color.holo_green_dark
            "connecting" -> android.R.color.holo_orange_dark
            "disconnected" -> android.R.color.holo_red_dark
            "error" -> android.R.color.holo_red_light
            else -> android.R.color.darker_gray
        }
    }
    
    /**
     * Convert bytes to megabytes
     */
    fun bytesToMB(bytes: Long): Double {
        return bytes / (1024.0 * 1024.0)
    }
    
    /**
     * Convert bytes to gigabytes
     */
    fun bytesToGB(bytes: Long): Double {
        return bytes / (1024.0 * 1024.0 * 1024.0)
    }
    
    /**
     * Format uptime from milliseconds
     */
    fun formatUptime(uptimeMs: Long): String {
        val days = TimeUnit.MILLISECONDS.toDays(uptimeMs)
        val hours = TimeUnit.MILLISECONDS.toHours(uptimeMs) % 24
        val minutes = TimeUnit.MILLISECONDS.toMinutes(uptimeMs) % 60
        
        return when {
            days > 0 -> "${days}d ${hours}h ${minutes}m"
            hours > 0 -> "${hours}h ${minutes}m"
            else -> "${minutes}m"
        }
    }
}
