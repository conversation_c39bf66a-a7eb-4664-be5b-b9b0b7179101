<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:padding="16dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- Title -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/settings"
            android:textSize="20sp"
            android:textStyle="bold"
            android:textColor="@color/text_primary"
            android:layout_marginBottom="16dp" />

        <!-- Connection Settings Card -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="12dp"
            app:cardCornerRadius="8dp"
            app:cardElevation="2dp"
            app:cardBackgroundColor="@color/background_secondary">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Connection Settings"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_primary"
                    android:layout_marginBottom="12dp" />

                <!-- Auto Connect -->
                <com.google.android.material.checkbox.MaterialCheckBox
                    android:id="@+id/cbAutoConnect"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Auto connect on startup"
                    android:layout_marginBottom="8dp" />

                <!-- Bypass LAN -->
                <com.google.android.material.checkbox.MaterialCheckBox
                    android:id="@+id/cbBypassLan"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Bypass LAN traffic"
                    android:checked="true"
                    android:layout_marginBottom="8dp" />

                <!-- DNS Servers -->
                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:boxStrokeColor="@color/vpn_primary"
                    app:hintTextColor="@color/vpn_primary">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/etDnsServers"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="DNS Servers (comma separated)"
                        android:inputType="text"
                        android:text="8.8.8.8,8.8.4.4" />

                </com.google.android.material.textfield.TextInputLayout>

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- App Settings Card -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="12dp"
            app:cardCornerRadius="8dp"
            app:cardElevation="2dp"
            app:cardBackgroundColor="@color/background_secondary">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="App Settings"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_primary"
                    android:layout_marginBottom="12dp" />

                <!-- Dark Theme -->
                <com.google.android.material.checkbox.MaterialCheckBox
                    android:id="@+id/cbDarkTheme"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Dark theme"
                    android:layout_marginBottom="8dp" />

                <!-- Show Notifications -->
                <com.google.android.material.checkbox.MaterialCheckBox
                    android:id="@+id/cbShowNotifications"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Show connection notifications"
                    android:checked="true"
                    android:layout_marginBottom="8dp" />

                <!-- Statistics Update Interval -->
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Statistics update interval"
                    android:textColor="@color/text_primary"
                    android:layout_marginTop="8dp"
                    android:layout_marginBottom="4dp" />

                <com.google.android.material.slider.Slider
                    android:id="@+id/sliderUpdateInterval"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:valueFrom="1"
                    android:valueTo="10"
                    android:value="3"
                    android:stepSize="1"
                    app:thumbColor="@color/vpn_primary"
                    app:trackColorActive="@color/vpn_primary" />

                <TextView
                    android:id="@+id/tvUpdateInterval"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="3 seconds"
                    android:textColor="@color/text_secondary"
                    android:textSize="12sp" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- Data Management Card -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="8dp"
            app:cardElevation="2dp"
            app:cardBackgroundColor="@color/background_secondary">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Data Management"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_primary"
                    android:layout_marginBottom="12dp" />

                <!-- Export Servers -->
                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnExportServers"
                    style="@style/Widget.Material3.Button.OutlinedButton"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Export server configurations"
                    android:layout_marginBottom="8dp" />

                <!-- Import Servers -->
                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnImportServers"
                    style="@style/Widget.Material3.Button.OutlinedButton"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Import server configurations"
                    android:layout_marginBottom="8dp" />

                <!-- Clear Data -->
                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnClearData"
                    style="@style/Widget.Material3.Button.OutlinedButton"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Clear all data"
                    android:textColor="@color/status_error"
                    app:strokeColor="@color/status_error" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- Buttons -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="end">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnCancel"
                style="@style/Widget.Material3.Button.TextButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="8dp"
                android:text="@string/cancel" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnSave"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/save" />

        </LinearLayout>

    </LinearLayout>

</ScrollView>
