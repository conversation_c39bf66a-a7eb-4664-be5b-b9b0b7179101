# Android VPN Application with VLESS Support

Современное Android VPN приложение с поддержкой протокола VLESS (3x-ui) и привлекательным пользовательским интерфейсом с GIF анимациями.

## 🚀 Особенности

### ✨ Основные функции
- **VLESS Protocol Support**: Полная поддержка протокола VLESS с различными транспортами
- **Multiple Transports**: TCP, WebSocket, gRPC, HTTP/2
- **Modern UI**: Material Design 3 с анимированными GIF элементами
- **Real-time Statistics**: Мониторинг скорости, трафика и качества соединения
- **Server Management**: Добавление, редактирование и управление серверами
- **QR Code Support**: Импорт конфигураций через QR коды
- **Auto-connect**: Автоматическое подключение к серверам
- **Dark/Light Theme**: Поддержка темной и светлой темы

### 📊 Мониторинг и статистика
- Отображение скорости загрузки/выгрузки в реальном времени
- Графики использования трафика
- Мониторинг пинга и качества соединения
- Статистика по сессиям, дням и месяцам
- Детальная статистика с интерактивными графиками

### 🎨 Пользовательский интерфейс
- Анимированные GIF индикаторы состояния подключения
- Плавные переходы и анимации
- Интуитивно понятный дизайн
- Адаптивный интерфейс для разных размеров экранов

## 🛠️ Технические детали

### Архитектура
- **MVVM Pattern**: Использование архитектурного паттерна MVVM
- **Kotlin Coroutines**: Асинхронные операции
- **StateFlow**: Реактивное программирование
- **ViewBinding**: Типобезопасная работа с UI
- **Material Design 3**: Современные UI компоненты

### Основные компоненты
- `VpnService`: Основной VPN сервис
- `VlessClient`: Клиент для VLESS протокола
- `StatisticsMonitor`: Мониторинг статистики
- `ConfigManager`: Управление конфигурациями
- `MainActivity`: Главная активность
- `StatisticsFragment`: Детальная статистика

### Зависимости
```gradle
// Networking
implementation 'com.squareup.okhttp3:okhttp:4.12.0'
implementation 'com.squareup.retrofit2:retrofit:2.9.0'

// UI Components
implementation 'com.google.android.material:material:1.11.0'
implementation 'androidx.recyclerview:recyclerview:1.3.2'

// GIF Support
implementation 'pl.droidsonroids.gif:android-gif-drawable:1.2.25'
implementation 'com.github.bumptech.glide:glide:4.16.0'
implementation 'com.airbnb.android:lottie:6.2.0'

// Charts
implementation 'com.github.PhilJay:MPAndroidChart:v3.1.0'

// QR Code
implementation 'com.journeyapps:zxing-android-embedded:4.3.0'

// JSON
implementation 'com.google.code.gson:gson:2.10.1'
```

## 📱 Установка и настройка

### Требования
- Android 7.0 (API level 24) или выше
- Разрешения: VPN, Camera (для QR кодов), Internet

### Сборка проекта
1. Клонируйте репозиторий
2. Откройте проект в Android Studio
3. Синхронизируйте Gradle файлы
4. Соберите и запустите приложение

### Настройка сервера
1. Откройте приложение
2. Нажмите кнопку "+" для добавления сервера
3. Введите данные VLESS сервера:
   - Имя сервера
   - Адрес и порт
   - UUID
   - Настройки шифрования и транспорта
4. Сохраните конфигурацию

### Подключение
1. Выберите сервер из списка
2. Нажмите кнопку подключения
3. Разрешите создание VPN соединения
4. Наблюдайте за статистикой в реальном времени

## 🎯 Использование

### Главный экран
- **Статус подключения**: Анимированный индикатор с GIF
- **Кнопка подключения**: Центральная кнопка управления
- **Список серверов**: Прокручиваемый список доступных серверов
- **Статистика**: Карточка с основными показателями
- **График скорости**: Визуализация трафика в реальном времени

### Управление серверами
- **Добавление**: Кнопка "+" или импорт QR кода
- **Редактирование**: Долгое нажатие на сервер
- **Удаление**: Меню сервера
- **Тестирование**: Автоматическая проверка пинга

### Детальная статистика
- **Графики в реальном времени**: Скорость загрузки/выгрузки
- **Использование данных**: По сессиям, дням, месяцам
- **Качество соединения**: Пинг, потери пакетов, время работы
- **Экспорт данных**: Сохранение статистики

## 🔧 Конфигурация

### Настройки приложения
- Автоподключение при запуске
- Выбор DNS серверов
- Обход локальных сетей
- Уведомления
- Тема интерфейса

### VLESS конфигурация
```json
{
  "name": "My Server",
  "address": "example.com",
  "port": 443,
  "uuid": "12345678-1234-1234-1234-123456789abc",
  "encryption": "none",
  "network": "ws",
  "type": "none",
  "host": "example.com",
  "path": "/path",
  "tls": "tls",
  "sni": "example.com"
}
```

## 🧪 Тестирование

### Запуск тестов
```bash
./gradlew test
./gradlew connectedAndroidTest
```

### Покрытие тестами
- Unit тесты для утилит и моделей данных
- Integration тесты для VPN сервиса
- UI тесты для основных сценариев

## 📄 Лицензия

Этот проект распространяется под лицензией MIT. См. файл `LICENSE` для подробностей.

## 🤝 Вклад в проект

Мы приветствуем вклад в развитие проекта! Пожалуйста:
1. Создайте fork репозитория
2. Создайте ветку для новой функции
3. Внесите изменения и добавьте тесты
4. Создайте Pull Request

## 📞 Поддержка

Если у вас есть вопросы или проблемы:
- Создайте Issue в GitHub
- Проверьте документацию
- Обратитесь к сообществу

## 🔄 Обновления

### Версия 1.0.0
- Первый релиз
- Поддержка VLESS протокола
- Базовый UI с анимациями
- Система мониторинга статистики
- Управление серверами

---

**Примечание**: Это приложение предназначено для образовательных целей. Убедитесь, что использование VPN соответствует законодательству вашей страны.
