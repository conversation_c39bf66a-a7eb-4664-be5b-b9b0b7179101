package com.androidvpn.ui

import android.Manifest
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.ServiceConnection
import android.content.pm.PackageManager
import android.net.VpnService
import android.os.Bundle
import android.os.IBinder
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AppCompatActivity
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.androidvpn.R
import com.androidvpn.adapter.ServerAdapter
import com.androidvpn.databinding.ActivityMainBinding
import com.androidvpn.model.ConnectionState
import com.androidvpn.model.VlessConfig
import com.androidvpn.service.VpnService as MyVpnService
import com.androidvpn.ui.dialog.AddServerDialog
import com.androidvpn.ui.dialog.SettingsDialog
import com.androidvpn.ui.fragment.StatisticsFragment
import com.androidvpn.utils.ConfigManager
import com.androidvpn.utils.FormatUtils
import com.androidvpn.utils.QrCodeScanner
import com.bumptech.glide.Glide
import com.github.mikephil.charting.components.XAxis
import com.github.mikephil.charting.data.Entry
import com.github.mikephil.charting.data.LineData
import com.github.mikephil.charting.data.LineDataSet
import kotlinx.coroutines.launch
import pl.droidsonroids.gif.GifImageView

class MainActivity : AppCompatActivity() {
    
    private lateinit var binding: ActivityMainBinding
    private lateinit var serverAdapter: ServerAdapter
    private lateinit var configManager: ConfigManager
    private lateinit var qrCodeScanner: QrCodeScanner
    
    private var vpnService: MyVpnService? = null
    private var isServiceBound = false
    
    private val speedEntries = mutableListOf<Entry>()
    private var entryIndex = 0f
    
    // VPN permission launcher
    private val vpnPermissionLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == RESULT_OK) {
            connectToSelectedServer()
        } else {
            Toast.makeText(this, getString(R.string.error_permission_denied), Toast.LENGTH_SHORT).show()
        }
    }
    
    // Camera permission launcher
    private val cameraPermissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestPermission()
    ) { isGranted ->
        if (isGranted) {
            qrCodeScanner.startScan()
        } else {
            Toast.makeText(this, "Camera permission required for QR scanning", Toast.LENGTH_SHORT).show()
        }
    }
    
    private val serviceConnection = object : ServiceConnection {
        override fun onServiceConnected(name: ComponentName?, service: IBinder?) {
            val binder = service as MyVpnService.VpnServiceBinder
            vpnService = binder.getService()
            isServiceBound = true
            observeConnectionState()
        }
        
        override fun onServiceDisconnected(name: ComponentName?) {
            vpnService = null
            isServiceBound = false
        }
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityMainBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        setupToolbar()
        setupUI()
        setupRecyclerView()
        setupChart()
        setupClickListeners()
        
        configManager = ConfigManager(this)
        qrCodeScanner = QrCodeScanner(this) { vlessUrl ->
            handleQrCodeResult(vlessUrl)
        }
        
        loadServers()
        bindVpnService()
    }
    
    override fun onDestroy() {
        super.onDestroy()
        if (isServiceBound) {
            unbindService(serviceConnection)
        }
    }
    
    private fun setupToolbar() {
        setSupportActionBar(binding.toolbar)
        supportActionBar?.setDisplayShowTitleEnabled(true)
    }
    
    private fun setupUI() {
        // Load connection status GIF
        loadConnectionStatusGif(ConnectionState.DISCONNECTED)

        // Initially hide statistics card
        binding.cardStatistics.visibility = android.view.View.GONE

        // Setup statistics card click listener
        binding.cardStatistics.setOnClickListener {
            showDetailedStatistics()
        }
    }
    
    private fun setupRecyclerView() {
        serverAdapter = ServerAdapter(
            onServerClick = { server -> selectServer(server) },
            onServerMenuClick = { server -> showServerMenu(server) }
        )
        
        binding.recyclerViewServers.apply {
            layoutManager = LinearLayoutManager(this@MainActivity)
            adapter = serverAdapter
        }
    }
    
    private fun setupChart() {
        binding.chartSpeed.apply {
            description.isEnabled = false
            setTouchEnabled(true)
            isDragEnabled = true
            setScaleEnabled(true)
            setPinchZoom(true)
            
            xAxis.apply {
                position = XAxis.XAxisPosition.BOTTOM
                setDrawGridLines(false)
                granularity = 1f
            }
            
            axisLeft.apply {
                setDrawGridLines(true)
                axisMinimum = 0f
            }
            
            axisRight.isEnabled = false
            legend.isEnabled = true
        }
    }
    
    private fun setupClickListeners() {
        binding.btnConnect.setOnClickListener {
            handleConnectButtonClick()
        }
        
        binding.btnAddServer.setOnClickListener {
            showAddServerDialog()
        }
        
        binding.btnScanQr.setOnClickListener {
            requestCameraPermissionAndScan()
        }
        
        binding.fabSettings.setOnClickListener {
            showSettingsDialog()
        }
    }
    
    private fun loadConnectionStatusGif(state: ConnectionState) {
        val gifResource = when (state) {
            ConnectionState.CONNECTED -> R.drawable.vpn_connected_animation
            ConnectionState.CONNECTING -> R.drawable.vpn_connecting_animation
            ConnectionState.DISCONNECTING -> R.drawable.vpn_disconnecting_animation
            ConnectionState.ERROR -> R.drawable.vpn_error_animation
            else -> R.drawable.vpn_disconnected_animation
        }
        
        // For now, just set the drawable resource
        // In a real app, you would load actual GIF files
        binding.gifConnectionStatus.setImageResource(gifResource)
    }
    
    private fun handleConnectButtonClick() {
        val currentState = vpnService?.connectionInfo?.value?.state ?: ConnectionState.DISCONNECTED
        
        when (currentState) {
            ConnectionState.DISCONNECTED -> {
                val selectedServer = configManager.getSelectedServer()
                if (selectedServer != null) {
                    requestVpnPermission()
                } else {
                    Toast.makeText(this, "Please select a server first", Toast.LENGTH_SHORT).show()
                }
            }
            ConnectionState.CONNECTED, ConnectionState.CONNECTING -> {
                vpnService?.disconnect()
            }
            else -> {
                // Do nothing for transitional states
            }
        }
    }
    
    private fun requestVpnPermission() {
        val intent = VpnService.prepare(this)
        if (intent != null) {
            vpnPermissionLauncher.launch(intent)
        } else {
            connectToSelectedServer()
        }
    }
    
    private fun connectToSelectedServer() {
        val selectedServer = configManager.getSelectedServer()
        selectedServer?.let { server ->
            val intent = Intent(this, MyVpnService::class.java).apply {
                putExtra(MyVpnService.EXTRA_CONFIG, server)
            }
            startService(intent)
        }
    }
    
    private fun requestCameraPermissionAndScan() {
        when {
            ContextCompat.checkSelfPermission(this, Manifest.permission.CAMERA) == 
                PackageManager.PERMISSION_GRANTED -> {
                qrCodeScanner.startScan()
            }
            else -> {
                cameraPermissionLauncher.launch(Manifest.permission.CAMERA)
            }
        }
    }
    
    private fun handleQrCodeResult(vlessUrl: String) {
        val config = VlessConfig.fromVlessUrl(vlessUrl)
        if (config != null) {
            configManager.addServer(config)
            loadServers()
            Toast.makeText(this, "Server added successfully", Toast.LENGTH_SHORT).show()
        } else {
            Toast.makeText(this, getString(R.string.invalid_qr), Toast.LENGTH_SHORT).show()
        }
    }
    
    private fun showAddServerDialog() {
        val dialog = AddServerDialog { config ->
            configManager.addServer(config)
            loadServers()
        }
        dialog.show(supportFragmentManager, "AddServerDialog")
    }
    
    private fun showSettingsDialog() {
        val dialog = SettingsDialog()
        dialog.show(supportFragmentManager, "SettingsDialog")
    }
    
    private fun selectServer(server: VlessConfig) {
        configManager.setSelectedServer(server)
        loadServers() // Refresh to show selection
        
        // Update UI to show selected server
        binding.tvServerName.text = server.name
        binding.tvServerName.visibility = android.view.View.VISIBLE
    }
    
    private fun showServerMenu(server: VlessConfig) {
        // Show popup menu with edit/delete options
        val popupMenu = androidx.appcompat.widget.PopupMenu(this, binding.recyclerViewServers)
        popupMenu.menuInflater.inflate(R.menu.server_menu, popupMenu.menu)
        
        popupMenu.setOnMenuItemClickListener { item ->
            when (item.itemId) {
                R.id.action_edit -> {
                    editServer(server)
                    true
                }
                R.id.action_delete -> {
                    deleteServer(server)
                    true
                }
                else -> false
            }
        }
        
        popupMenu.show()
    }
    
    private fun editServer(server: VlessConfig) {
        val dialog = AddServerDialog(server) { updatedConfig ->
            configManager.updateServer(updatedConfig)
            loadServers()
        }
        dialog.show(supportFragmentManager, "EditServerDialog")
    }
    
    private fun deleteServer(server: VlessConfig) {
        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle(getString(R.string.confirm_delete))
            .setMessage(getString(R.string.confirm_delete_message))
            .setPositiveButton(getString(R.string.delete)) { _, _ ->
                configManager.deleteServer(server)
                loadServers()
            }
            .setNegativeButton(getString(R.string.cancel), null)
            .show()
    }
    
    private fun loadServers() {
        val servers = configManager.getAllServers()
        serverAdapter.updateServers(servers)
        
        val selectedServer = configManager.getSelectedServer()
        selectedServer?.let {
            binding.tvServerName.text = it.name
            binding.tvServerName.visibility = android.view.View.VISIBLE
        }
    }
    
    private fun bindVpnService() {
        val intent = Intent(this, MyVpnService::class.java)
        bindService(intent, serviceConnection, Context.BIND_AUTO_CREATE)
    }
    
    private fun observeConnectionState() {
        vpnService?.let { service ->
            lifecycleScope.launch {
                service.connectionInfo.collect { connectionInfo ->
                    updateUI(connectionInfo.state)
                    updateStatistics(connectionInfo.stats)
                    
                    if (connectionInfo.errorMessage != null) {
                        Toast.makeText(this@MainActivity, connectionInfo.errorMessage, Toast.LENGTH_LONG).show()
                    }
                }
            }
        }
    }
    
    private fun updateUI(state: ConnectionState) {
        // Update connection button
        when (state) {
            ConnectionState.DISCONNECTED -> {
                binding.btnConnect.text = getString(R.string.connect)
                binding.btnConnect.setBackgroundColor(ContextCompat.getColor(this, R.color.button_connect))
                binding.tvConnectionStatus.text = getString(R.string.disconnected)
                binding.tvConnectionStatus.setTextColor(ContextCompat.getColor(this, R.color.status_disconnected))
                binding.cardStatistics.visibility = android.view.View.GONE
            }
            ConnectionState.CONNECTING -> {
                binding.btnConnect.text = getString(R.string.connecting)
                binding.btnConnect.setBackgroundColor(ContextCompat.getColor(this, R.color.status_connecting))
                binding.tvConnectionStatus.text = getString(R.string.connecting)
                binding.tvConnectionStatus.setTextColor(ContextCompat.getColor(this, R.color.status_connecting))
            }
            ConnectionState.CONNECTED -> {
                binding.btnConnect.text = getString(R.string.disconnect)
                binding.btnConnect.setBackgroundColor(ContextCompat.getColor(this, R.color.button_disconnect))
                binding.tvConnectionStatus.text = getString(R.string.connected)
                binding.tvConnectionStatus.setTextColor(ContextCompat.getColor(this, R.color.status_connected))
                binding.cardStatistics.visibility = android.view.View.VISIBLE
            }
            ConnectionState.DISCONNECTING -> {
                binding.btnConnect.text = getString(R.string.disconnecting)
                binding.btnConnect.setBackgroundColor(ContextCompat.getColor(this, R.color.status_connecting))
                binding.tvConnectionStatus.text = getString(R.string.disconnecting)
                binding.tvConnectionStatus.setTextColor(ContextCompat.getColor(this, R.color.status_connecting))
            }
            ConnectionState.ERROR -> {
                binding.btnConnect.text = getString(R.string.connect)
                binding.btnConnect.setBackgroundColor(ContextCompat.getColor(this, R.color.button_connect))
                binding.tvConnectionStatus.text = getString(R.string.connection_failed)
                binding.tvConnectionStatus.setTextColor(ContextCompat.getColor(this, R.color.status_error))
                binding.cardStatistics.visibility = android.view.View.GONE
            }
        }
        
        // Update connection status GIF
        loadConnectionStatusGif(state)
    }
    
    private fun updateStatistics(stats: com.androidvpn.model.ConnectionStats) {
        // Update text statistics
        binding.tvUploadSpeed.text = FormatUtils.formatSpeed(stats.uploadSpeed)
        binding.tvDownloadSpeed.text = FormatUtils.formatSpeed(stats.downloadSpeed)
        binding.tvTotalUpload.text = FormatUtils.formatBytes(stats.totalUpload)
        binding.tvTotalDownload.text = FormatUtils.formatBytes(stats.totalDownload)
        binding.tvConnectionTime.text = FormatUtils.formatDuration(stats.connectionTime)
        binding.tvPing.text = "${stats.ping} ${getString(R.string.milliseconds)}"
        
        // Update speed chart
        updateSpeedChart(stats.downloadSpeed.toFloat())
    }
    
    private fun updateSpeedChart(speed: Float) {
        speedEntries.add(Entry(entryIndex++, speed))
        
        // Keep only last 60 entries (1 minute of data)
        if (speedEntries.size > 60) {
            speedEntries.removeAt(0)
            // Adjust x values
            speedEntries.forEachIndexed { index, entry ->
                entry.x = index.toFloat()
            }
            entryIndex = speedEntries.size.toFloat()
        }
        
        val dataSet = LineDataSet(speedEntries, "Download Speed").apply {
            color = ContextCompat.getColor(this@MainActivity, R.color.chart_download)
            setCircleColor(ContextCompat.getColor(this@MainActivity, R.color.chart_download))
            lineWidth = 2f
            circleRadius = 3f
            setDrawCircleHole(false)
            valueTextSize = 0f
        }
        
        val lineData = LineData(dataSet)
        binding.chartSpeed.data = lineData
        binding.chartSpeed.invalidate()
    }

    private fun showDetailedStatistics() {
        val fragment = StatisticsFragment.newInstance()
        supportFragmentManager.beginTransaction()
            .replace(android.R.id.content, fragment)
            .addToBackStack("statistics")
            .commit()
    }
}
