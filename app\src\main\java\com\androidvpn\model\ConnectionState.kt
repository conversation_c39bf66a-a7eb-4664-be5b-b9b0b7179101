package com.androidvpn.model

/**
 * VPN connection states
 */
enum class ConnectionState {
    DISCONNECTED,
    CONNECTING,
    CONNECTED,
    DISCONNECTING,
    ERROR
}

/**
 * Connection statistics
 */
data class ConnectionStats(
    val uploadSpeed: Long = 0L, // bytes per second
    val downloadSpeed: Long = 0L, // bytes per second
    val totalUpload: Long = 0L, // total bytes uploaded
    val totalDownload: Long = 0L, // total bytes downloaded
    val connectionTime: Long = 0L, // connection duration in milliseconds
    val ping: Int = 0 // ping in milliseconds
)

/**
 * VPN connection info
 */
data class VpnConnectionInfo(
    val state: ConnectionState = ConnectionState.DISCONNECTED,
    val config: VlessConfig? = null,
    val stats: ConnectionStats = ConnectionStats(),
    val errorMessage: String? = null,
    val connectedAt: Long? = null
)
