package com.androidvpn.utils

import androidx.activity.ComponentActivity
import androidx.activity.result.contract.ActivityResultContracts
import com.journeyapps.barcodescanner.ScanContract
import com.journeyapps.barcodescanner.ScanIntentResult
import com.journeyapps.barcodescanner.ScanOptions

/**
 * QR Code scanner utility for importing VLESS configurations
 */
class QrCodeScanner(
    private val activity: ComponentActivity,
    private val onResult: (String) -> Unit
) {
    
    private val scanLauncher = activity.registerForActivityResult(ScanContract()) { result ->
        handleScanResult(result)
    }
    
    /**
     * Start QR code scanning
     */
    fun startScan() {
        val options = ScanOptions().apply {
            setDesiredBarcodeFormats(ScanOptions.QR_CODE)
            setPrompt("Scan VLESS QR Code")
            setCameraId(0) // Use rear camera
            setBeepEnabled(true)
            setBarcodeImageEnabled(true)
            setOrientationLocked(false)
        }
        
        scanLauncher.launch(options)
    }
    
    private fun handleScanResult(result: ScanIntentResult) {
        if (result.contents != null) {
            val scannedText = result.contents
            
            // Validate that it's a VLESS URL
            if (isValidVlessUrl(scannedText)) {
                onResult(scannedText)
            } else {
                // Try to extract VLESS URL from the scanned text
                val extractedUrl = extractVlessUrl(scannedText)
                if (extractedUrl != null) {
                    onResult(extractedUrl)
                } else {
                    onResult("") // Invalid QR code
                }
            }
        }
    }
    
    /**
     * Check if the scanned text is a valid VLESS URL
     */
    private fun isValidVlessUrl(text: String): Boolean {
        return text.startsWith("vless://", ignoreCase = true)
    }
    
    /**
     * Try to extract VLESS URL from text that might contain multiple lines or additional info
     */
    private fun extractVlessUrl(text: String): String? {
        val lines = text.split("\n", "\r\n", "\r")
        
        for (line in lines) {
            val trimmedLine = line.trim()
            if (isValidVlessUrl(trimmedLine)) {
                return trimmedLine
            }
        }
        
        // Try to find VLESS URL within the text
        val vlessRegex = Regex("vless://[^\\s]+", RegexOption.IGNORE_CASE)
        val match = vlessRegex.find(text)
        return match?.value
    }
}
