package com.androidvpn.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.androidvpn.R
import com.androidvpn.databinding.ItemServerBinding
import com.androidvpn.model.VlessConfig
import com.bumptech.glide.Glide

class ServerAdapter(
    private val onServerClick: (VlessConfig) -> Unit,
    private val onServerMenuClick: (VlessConfig) -> Unit
) : ListAdapter<VlessConfig, ServerAdapter.ServerViewHolder>(ServerDiffCallback()) {
    
    private var selectedServerId: String? = null
    
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ServerViewHolder {
        val binding = ItemServerBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return ServerViewHolder(binding)
    }
    
    override fun onBindViewHolder(holder: ServerViewHolder, position: Int) {
        holder.bind(getItem(position))
    }
    
    fun updateServers(servers: List<VlessConfig>) {
        submitList(servers)
    }
    
    fun setSelectedServer(serverId: String?) {
        selectedServerId = serverId
        notifyDataSetChanged()
    }
    
    inner class ServerViewHolder(
        private val binding: ItemServerBinding
    ) : RecyclerView.ViewHolder(binding.root) {
        
        fun bind(server: VlessConfig) {
            binding.apply {
                // Server info
                tvServerName.text = server.name
                tvServerAddress.text = "${server.address}:${server.port}"
                
                // Network and security chips
                chipNetwork.text = when (server.network) {
                    "tcp" -> "TCP"
                    "ws" -> "WebSocket"
                    "grpc" -> "gRPC"
                    "h2" -> "HTTP/2"
                    else -> server.network.uppercase()
                }
                
                chipSecurity.text = when (server.security) {
                    "none" -> "None"
                    "tls" -> "TLS"
                    "reality" -> "Reality"
                    else -> server.security.uppercase()
                }
                
                // Update chip colors based on security
                val securityColor = when (server.security) {
                    "tls", "reality" -> R.color.status_connected
                    else -> R.color.status_connecting
                }
                chipSecurity.setChipBackgroundColorResource(securityColor)
                
                // Connection indicator
                val isSelected = server.id == selectedServerId
                val indicatorColor = if (isSelected) {
                    R.color.status_connected
                } else {
                    R.color.status_disconnected
                }
                viewConnectionIndicator.backgroundTintList = 
                    ContextCompat.getColorStateList(root.context, indicatorColor)
                
                // Server status GIF
                loadServerStatusGif(server)
                
                // Ping and signal strength (mock data for now)
                updatePingAndSignal(server)
                
                // Click listeners
                root.setOnClickListener {
                    onServerClick(server)
                }
                
                btnServerMenu.setOnClickListener {
                    onServerMenuClick(server)
                }
            }
        }
        
        private fun loadServerStatusGif(server: VlessConfig) {
            // Load appropriate GIF based on server status
            val gifResource = if (server.id == selectedServerId) {
                R.drawable.server_active_animation
            } else {
                R.drawable.server_idle_animation
            }
            
            // For now, just set the drawable resource
            // In a real app, you would load actual GIF files
            binding.gifServerStatus.setImageResource(gifResource)
        }
        
        private fun updatePingAndSignal(server: VlessConfig) {
            // Mock ping data (in a real app, you would measure actual ping)
            val mockPing = (20..200).random()
            binding.tvPing.text = "${mockPing}ms"
            
            // Update ping color based on value
            val pingColor = when {
                mockPing < 50 -> R.color.status_connected
                mockPing < 100 -> R.color.status_connecting
                else -> R.color.status_error
            }
            binding.tvPing.setTextColor(
                ContextCompat.getColor(binding.root.context, pingColor)
            )
            
            // Update signal bars based on ping
            val signalStrength = when {
                mockPing < 50 -> 4
                mockPing < 100 -> 3
                mockPing < 150 -> 2
                else -> 1
            }
            
            updateSignalBars(signalStrength)
        }
        
        private fun updateSignalBars(strength: Int) {
            val bars = listOf(
                binding.signalBar1,
                binding.signalBar2,
                binding.signalBar3,
                binding.signalBar4
            )
            
            bars.forEachIndexed { index, bar ->
                val color = if (index < strength) {
                    R.color.status_connected
                } else {
                    R.color.status_disconnected
                }
                bar.backgroundTintList = 
                    ContextCompat.getColorStateList(binding.root.context, color)
            }
        }
    }
    
    private class ServerDiffCallback : DiffUtil.ItemCallback<VlessConfig>() {
        override fun areItemsTheSame(oldItem: VlessConfig, newItem: VlessConfig): Boolean {
            return oldItem.id == newItem.id
        }
        
        override fun areContentsTheSame(oldItem: VlessConfig, newItem: VlessConfig): Boolean {
            return oldItem == newItem
        }
    }
}
