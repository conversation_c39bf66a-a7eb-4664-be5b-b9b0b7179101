#!/bin/bash

echo "Building Android VPN APK..."
echo

# Check if Android SDK is available
if [ -z "$ANDROID_HOME" ]; then
    echo "ERROR: ANDROID_HOME is not set!"
    echo "Please set ANDROID_HOME to your Android SDK path"
    echo "Example: export ANDROID_HOME=~/Android/Sdk"
    exit 1
fi

# Check if Java is available
if ! command -v java &> /dev/null; then
    echo "ERROR: Java is not found in PATH!"
    echo "Please install Java JDK 8 or higher"
    exit 1
fi

echo "Android SDK: $ANDROID_HOME"
echo

# Make gradlew executable
if [ -f "gradlew" ]; then
    chmod +x gradlew
fi

# Create gradle wrapper if it doesn't exist
if [ ! -f "gradle/wrapper/gradle-wrapper.jar" ]; then
    echo "Creating Gradle Wrapper..."
    gradle wrapper --gradle-version 8.0
fi

# Clean previous builds
echo "Cleaning previous builds..."
if [ -f "gradlew" ]; then
    ./gradlew clean
else
    gradle clean
fi

# Build debug APK
echo "Building debug APK..."
if [ -f "gradlew" ]; then
    ./gradlew assembleDebug
else
    gradle assembleDebug
fi

if [ $? -ne 0 ]; then
    echo
    echo "BUILD FAILED!"
    echo "Check the error messages above"
    exit 1
fi

echo
echo "BUILD SUCCESSFUL!"
echo

APK_PATH="app/build/outputs/apk/debug/app-debug.apk"
echo "APK location: $APK_PATH"
echo

# Check if APK was created
if [ -f "$APK_PATH" ]; then
    echo "APK file size: $(du -h "$APK_PATH" | cut -f1)"
    echo
    echo "You can now install this APK on your Android device!"
    echo "Command: adb install $APK_PATH"
else
    echo "WARNING: APK file not found at expected location"
    echo "Check app/build/outputs/apk/ directory for APK files"
fi

echo
