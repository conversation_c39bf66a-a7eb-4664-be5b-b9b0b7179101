@echo off
echo Building Android VPN APK...
echo.

REM Check if Android SDK is available
if not defined ANDROID_HOME (
    echo ERROR: ANDROID_HOME is not set!
    echo Please set ANDROID_HOME to your Android SDK path
    echo Example: set ANDROID_HOME=C:\Users\<USER>\AppData\Local\Android\Sdk
    pause
    exit /b 1
)

REM Check if Java is available
java -version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Java is not found in PATH!
    echo Please install Java JDK 8 or higher
    pause
    exit /b 1
)

echo Android SDK: %ANDROID_HOME%
echo.

REM Create gradle wrapper if it doesn't exist
if not exist "gradle\wrapper\gradle-wrapper.jar" (
    echo Creating Gradle Wrapper...
    gradle wrapper --gradle-version 8.0
)

REM Clean previous builds
echo Cleaning previous builds...
if exist "gradlew.bat" (
    call gradlew.bat clean
) else (
    gradle clean
)

REM Build debug APK
echo Building debug APK...
if exist "gradlew.bat" (
    call gradlew.bat assembleDebug
) else (
    gradle assembleDebug
)

if errorlevel 1 (
    echo.
    echo BUILD FAILED!
    echo Check the error messages above
    pause
    exit /b 1
)

echo.
echo BUILD SUCCESSFUL!
echo.
echo APK location: app\build\outputs\apk\debug\app-debug.apk
echo.

REM Check if APK was created
if exist "app\build\outputs\apk\debug\app-debug.apk" (
    echo APK file size:
    dir "app\build\outputs\apk\debug\app-debug.apk" | find "app-debug.apk"
    echo.
    echo You can now install this APK on your Android device!
    echo Command: adb install app\build\outputs\apk\debug\app-debug.apk
) else (
    echo WARNING: APK file not found at expected location
    echo Check app\build\outputs\apk\ directory for APK files
)

echo.
pause
