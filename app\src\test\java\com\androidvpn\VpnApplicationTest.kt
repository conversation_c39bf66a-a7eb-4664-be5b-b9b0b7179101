package com.androidvpn

import com.androidvpn.model.ConnectionStats
import com.androidvpn.model.VlessConfig
import com.androidvpn.utils.ConfigManager
import com.androidvpn.utils.FormatUtils
import com.androidvpn.utils.StatisticsMonitor
import org.junit.Test
import org.junit.Assert.*
import org.junit.Before
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import android.content.Context

/**
 * Test class for VPN application components
 */
class VpnApplicationTest {

    @Mock
    private lateinit var mockContext: Context

    @Before
    fun setup() {
        MockitoAnnotations.openMocks(this)
    }

    @Test
    fun testVlessConfigCreation() {
        val config = VlessConfig(
            id = "test-id",
            name = "Test Server",
            address = "example.com",
            port = 443,
            uuid = "12345678-1234-1234-1234-123456789abc",
            encryption = "none",
            flow = "",
            network = "tcp",
            type = "none",
            host = "",
            path = "",
            tls = "tls",
            sni = "example.com",
            alpn = "",
            fingerprint = ""
        )

        assertEquals("Test Server", config.name)
        assertEquals("example.com", config.address)
        assertEquals(443, config.port)
        assertEquals("12345678-1234-1234-1234-123456789abc", config.uuid)
        assertTrue(config.isValid())
    }

    @Test
    fun testConnectionStatsFormatting() {
        val stats = ConnectionStats(
            uploadSpeed = 1024 * 1024, // 1 MB/s
            downloadSpeed = 2 * 1024 * 1024, // 2 MB/s
            totalUpload = 100 * 1024 * 1024, // 100 MB
            totalDownload = 500 * 1024 * 1024, // 500 MB
            connectionTime = 3661000, // 1 hour, 1 minute, 1 second
            ping = 50,
            packetLoss = 1.5,
            isConnected = true
        )

        assertEquals("1.0 MB/s", stats.getFormattedUploadSpeed())
        assertEquals("2.0 MB/s", stats.getFormattedDownloadSpeed())
        assertEquals("100.0 MB", stats.getFormattedTotalUpload())
        assertEquals("500.0 MB", stats.getFormattedTotalDownload())
        assertEquals("01:01:01", stats.getFormattedConnectionTime())
        assertEquals("50ms", stats.getFormattedPing())
        assertEquals("1.5%", stats.getFormattedPacketLoss())
    }

    @Test
    fun testFormatUtilsSpeed() {
        assertEquals("0 B/s", FormatUtils.formatSpeed(0))
        assertEquals("512 B/s", FormatUtils.formatSpeed(512))
        assertEquals("1.0 KB/s", FormatUtils.formatSpeed(1024))
        assertEquals("1.5 KB/s", FormatUtils.formatSpeed(1536))
        assertEquals("1.0 MB/s", FormatUtils.formatSpeed(1024 * 1024))
        assertEquals("1.0 GB/s", FormatUtils.formatSpeed(1024 * 1024 * 1024))
    }

    @Test
    fun testFormatUtilsBytes() {
        assertEquals("0 B", FormatUtils.formatBytes(0))
        assertEquals("512 B", FormatUtils.formatBytes(512))
        assertEquals("1.0 KB", FormatUtils.formatBytes(1024))
        assertEquals("1.5 KB", FormatUtils.formatBytes(1536))
        assertEquals("1.0 MB", FormatUtils.formatBytes(1024 * 1024))
        assertEquals("1.0 GB", FormatUtils.formatBytes(1024 * 1024 * 1024))
    }

    @Test
    fun testFormatUtilsDuration() {
        assertEquals("00:00:00", FormatUtils.formatDuration(0))
        assertEquals("00:00:30", FormatUtils.formatDuration(30000))
        assertEquals("00:01:00", FormatUtils.formatDuration(60000))
        assertEquals("01:00:00", FormatUtils.formatDuration(3600000))
        assertEquals("01:01:01", FormatUtils.formatDuration(3661000))
    }

    @Test
    fun testFormatUtilsDurationShort() {
        assertEquals("0s", FormatUtils.formatDurationShort(0))
        assertEquals("30s", FormatUtils.formatDurationShort(30000))
        assertEquals("1m 0s", FormatUtils.formatDurationShort(60000))
        assertEquals("1h 0m", FormatUtils.formatDurationShort(3600000))
        assertEquals("1h 1m", FormatUtils.formatDurationShort(3661000))
    }

    @Test
    fun testConnectionQuality() {
        // Test excellent quality
        val excellentStats = ConnectionStats(ping = 30, packetLoss = 0.5, isConnected = true)
        assertEquals(
            com.androidvpn.model.ConnectionQuality.EXCELLENT,
            excellentStats.getConnectionQuality()
        )

        // Test good quality
        val goodStats = ConnectionStats(ping = 80, packetLoss = 1.5, isConnected = true)
        assertEquals(
            com.androidvpn.model.ConnectionQuality.GOOD,
            goodStats.getConnectionQuality()
        )

        // Test fair quality
        val fairStats = ConnectionStats(ping = 150, packetLoss = 3.0, isConnected = true)
        assertEquals(
            com.androidvpn.model.ConnectionQuality.FAIR,
            fairStats.getConnectionQuality()
        )

        // Test poor quality
        val poorStats = ConnectionStats(ping = 300, packetLoss = 8.0, isConnected = true)
        assertEquals(
            com.androidvpn.model.ConnectionQuality.POOR,
            poorStats.getConnectionQuality()
        )

        // Test disconnected
        val disconnectedStats = ConnectionStats(isConnected = false)
        assertEquals(
            com.androidvpn.model.ConnectionQuality.DISCONNECTED,
            disconnectedStats.getConnectionQuality()
        )
    }

    @Test
    fun testVlessConfigValidation() {
        // Valid config
        val validConfig = VlessConfig(
            id = "test-id",
            name = "Test Server",
            address = "example.com",
            port = 443,
            uuid = "12345678-1234-1234-1234-123456789abc",
            encryption = "none"
        )
        assertTrue(validConfig.isValid())

        // Invalid config - empty name
        val invalidNameConfig = validConfig.copy(name = "")
        assertFalse(invalidNameConfig.isValid())

        // Invalid config - empty address
        val invalidAddressConfig = validConfig.copy(address = "")
        assertFalse(invalidAddressConfig.isValid())

        // Invalid config - invalid port
        val invalidPortConfig = validConfig.copy(port = 0)
        assertFalse(invalidPortConfig.isValid())

        // Invalid config - empty UUID
        val invalidUuidConfig = validConfig.copy(uuid = "")
        assertFalse(invalidUuidConfig.isValid())
    }

    @Test
    fun testConnectionStatsCalculations() {
        val stats = ConnectionStats(
            uploadSpeed = 1000,
            downloadSpeed = 2000,
            totalUpload = 5000,
            totalDownload = 10000
        )

        assertEquals(15000L, stats.getTotalDataTransferred())
        assertEquals(1500L, stats.getAverageSpeed())
        assertEquals("14.6 KB", stats.getFormattedTotalDataTransferred())
        assertEquals("1.5 KB/s", stats.getFormattedAverageSpeed())
    }

    @Test
    fun testPingFormatting() {
        assertEquals("N/A", FormatUtils.formatPing(-1))
        assertEquals("0ms", FormatUtils.formatPing(0))
        assertEquals("50ms", FormatUtils.formatPing(50))
        assertEquals("999ms", FormatUtils.formatPing(999))
    }

    @Test
    fun testSignalStrengthCalculation() {
        assertEquals(4, FormatUtils.calculateSignalStrength(30))  // Excellent
        assertEquals(3, FormatUtils.calculateSignalStrength(80))  // Good
        assertEquals(2, FormatUtils.calculateSignalStrength(150)) // Fair
        assertEquals(1, FormatUtils.calculateSignalStrength(300)) // Poor
        assertEquals(0, FormatUtils.calculateSignalStrength(-1))  // Unknown
    }

    @Test
    fun testVlessConfigToString() {
        val config = VlessConfig(
            id = "test-id",
            name = "Test Server",
            address = "example.com",
            port = 443,
            uuid = "12345678-1234-1234-1234-123456789abc",
            encryption = "none"
        )

        val configString = config.toString()
        assertTrue(configString.contains("Test Server"))
        assertTrue(configString.contains("example.com"))
        assertTrue(configString.contains("443"))
    }
}
