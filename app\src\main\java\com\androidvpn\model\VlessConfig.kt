package com.androidvpn.model

import com.google.gson.annotations.SerializedName
import java.util.*

/**
 * VLESS configuration model for 3x-ui panel
 */
data class VlessConfig(
    @SerializedName("id")
    val id: String = UUID.randomUUID().toString(),
    
    @SerializedName("name")
    val name: String,
    
    @SerializedName("address")
    val address: String,
    
    @SerializedName("port")
    val port: Int,
    
    @SerializedName("uuid")
    val uuid: String,
    
    @SerializedName("security")
    val security: String = "none", // none, tls, reality
    
    @SerializedName("network")
    val network: String = "tcp", // tcp, ws, grpc, h2
    
    @SerializedName("path")
    val path: String? = null, // for websocket
    
    @SerializedName("host")
    val host: String? = null, // for websocket/http2
    
    @SerializedName("serviceName")
    val serviceName: String? = null, // for grpc
    
    @SerializedName("alpn")
    val alpn: List<String>? = null, // for tls
    
    @SerializedName("sni")
    val sni: String? = null, // server name indication
    
    @SerializedName("fingerprint")
    val fingerprint: String? = null, // for reality
    
    @SerializedName("publicKey")
    val publicKey: String? = null, // for reality
    
    @SerializedName("shortId")
    val shortId: String? = null, // for reality
    
    @SerializedName("spiderX")
    val spiderX: String? = null, // for reality
    
    @SerializedName("flow")
    val flow: String? = null, // xtls flow control
    
    @SerializedName("allowInsecure")
    val allowInsecure: Boolean = false,
    
    @SerializedName("isActive")
    val isActive: Boolean = false,
    
    @SerializedName("createdAt")
    val createdAt: Long = System.currentTimeMillis(),
    
    @SerializedName("lastConnected")
    var lastConnected: Long? = null
) {
    
    /**
     * Generate VLESS URL for sharing
     */
    fun toVlessUrl(): String {
        val params = mutableMapOf<String, String>()
        
        // Add security
        params["security"] = security
        
        // Add network type
        params["type"] = network
        
        // Add network-specific parameters
        when (network) {
            "ws" -> {
                path?.let { params["path"] = it }
                host?.let { params["host"] = it }
            }
            "grpc" -> {
                serviceName?.let { params["serviceName"] = it }
            }
            "h2" -> {
                path?.let { params["path"] = it }
                host?.let { params["host"] = it }
            }
        }
        
        // Add TLS parameters
        if (security == "tls") {
            sni?.let { params["sni"] = it }
            alpn?.let { params["alpn"] = it.joinToString(",") }
            if (allowInsecure) params["allowInsecure"] = "1"
        }
        
        // Add Reality parameters
        if (security == "reality") {
            publicKey?.let { params["pbk"] = it }
            fingerprint?.let { params["fp"] = it }
            sni?.let { params["sni"] = it }
            shortId?.let { params["sid"] = it }
            spiderX?.let { params["spx"] = it }
        }
        
        // Add flow control
        flow?.let { params["flow"] = it }
        
        val paramString = params.map { "${it.key}=${it.value}" }.joinToString("&")
        
        return "vless://$uuid@$address:$port?$paramString#${name.replace(" ", "%20")}"
    }
    
    companion object {
        /**
         * Parse VLESS URL to VlessConfig
         */
        fun fromVlessUrl(url: String): VlessConfig? {
            try {
                if (!url.startsWith("vless://")) return null
                
                val withoutScheme = url.substring(8)
                val parts = withoutScheme.split("?", "#")
                if (parts.size < 2) return null
                
                val userInfo = parts[0]
                val params = parts[1]
                val name = if (parts.size > 2) parts[2].replace("%20", " ") else "Imported Server"
                
                val userParts = userInfo.split("@")
                if (userParts.size != 2) return null
                
                val uuid = userParts[0]
                val serverParts = userParts[1].split(":")
                if (serverParts.size != 2) return null
                
                val address = serverParts[0]
                val port = serverParts[1].toIntOrNull() ?: return null
                
                val paramMap = params.split("&").associate {
                    val kv = it.split("=", limit = 2)
                    if (kv.size == 2) kv[0] to kv[1] else kv[0] to ""
                }
                
                return VlessConfig(
                    name = name,
                    address = address,
                    port = port,
                    uuid = uuid,
                    security = paramMap["security"] ?: "none",
                    network = paramMap["type"] ?: "tcp",
                    path = paramMap["path"],
                    host = paramMap["host"],
                    serviceName = paramMap["serviceName"],
                    sni = paramMap["sni"],
                    fingerprint = paramMap["fp"],
                    publicKey = paramMap["pbk"],
                    shortId = paramMap["sid"],
                    spiderX = paramMap["spx"],
                    flow = paramMap["flow"],
                    alpn = paramMap["alpn"]?.split(","),
                    allowInsecure = paramMap["allowInsecure"] == "1"
                )
            } catch (e: Exception) {
                return null
            }
        }
    }
}
