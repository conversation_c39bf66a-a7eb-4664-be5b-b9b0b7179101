package com.androidvpn.utils

import android.content.Context
import android.content.SharedPreferences
import com.androidvpn.model.VlessConfig
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken

/**
 * Manager for handling VLESS server configurations
 */
class ConfigManager(private val context: Context) {
    
    companion object {
        private const val PREFS_NAME = "vpn_configs"
        private const val KEY_SERVERS = "servers"
        private const val KEY_SELECTED_SERVER = "selected_server"
        private const val KEY_AUTO_CONNECT = "auto_connect"
        private const val KEY_BYPASS_LAN = "bypass_lan"
        private const val KEY_DNS_SERVERS = "dns_servers"
    }
    
    private val prefs: SharedPreferences = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
    private val gson = Gson()
    
    /**
     * Get all saved servers
     */
    fun getAllServers(): List<VlessConfig> {
        val serversJson = prefs.getString(KEY_SERVERS, null) ?: return emptyList()
        val type = object : TypeToken<List<VlessConfig>>() {}.type
        return try {
            gson.fromJson(serversJson, type) ?: emptyList()
        } catch (e: Exception) {
            emptyList()
        }
    }
    
    /**
     * Add a new server configuration
     */
    fun addServer(config: VlessConfig) {
        val servers = getAllServers().toMutableList()
        
        // Check if server with same address and port already exists
        val existingIndex = servers.indexOfFirst { 
            it.address == config.address && it.port == config.port 
        }
        
        if (existingIndex >= 0) {
            // Update existing server
            servers[existingIndex] = config
        } else {
            // Add new server
            servers.add(config)
        }
        
        saveServers(servers)
    }
    
    /**
     * Update an existing server configuration
     */
    fun updateServer(config: VlessConfig) {
        val servers = getAllServers().toMutableList()
        val index = servers.indexOfFirst { it.id == config.id }
        
        if (index >= 0) {
            servers[index] = config
            saveServers(servers)
        }
    }
    
    /**
     * Delete a server configuration
     */
    fun deleteServer(config: VlessConfig) {
        val servers = getAllServers().toMutableList()
        servers.removeAll { it.id == config.id }
        saveServers(servers)
        
        // If deleted server was selected, clear selection
        if (getSelectedServer()?.id == config.id) {
            clearSelectedServer()
        }
    }
    
    /**
     * Get the currently selected server
     */
    fun getSelectedServer(): VlessConfig? {
        val selectedId = prefs.getString(KEY_SELECTED_SERVER, null) ?: return null
        return getAllServers().find { it.id == selectedId }
    }
    
    /**
     * Set the selected server
     */
    fun setSelectedServer(config: VlessConfig) {
        prefs.edit()
            .putString(KEY_SELECTED_SERVER, config.id)
            .apply()
    }
    
    /**
     * Clear the selected server
     */
    fun clearSelectedServer() {
        prefs.edit()
            .remove(KEY_SELECTED_SERVER)
            .apply()
    }
    
    /**
     * Get server by ID
     */
    fun getServerById(id: String): VlessConfig? {
        return getAllServers().find { it.id == id }
    }
    
    /**
     * Import servers from JSON string
     */
    fun importServers(jsonString: String): Boolean {
        return try {
            val type = object : TypeToken<List<VlessConfig>>() {}.type
            val importedServers: List<VlessConfig> = gson.fromJson(jsonString, type)
            
            val currentServers = getAllServers().toMutableList()
            
            importedServers.forEach { importedServer ->
                // Check for duplicates
                val existingIndex = currentServers.indexOfFirst { 
                    it.address == importedServer.address && it.port == importedServer.port 
                }
                
                if (existingIndex >= 0) {
                    currentServers[existingIndex] = importedServer
                } else {
                    currentServers.add(importedServer)
                }
            }
            
            saveServers(currentServers)
            true
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * Export all servers to JSON string
     */
    fun exportServers(): String {
        return gson.toJson(getAllServers())
    }
    
    /**
     * Get auto-connect setting
     */
    fun isAutoConnectEnabled(): Boolean {
        return prefs.getBoolean(KEY_AUTO_CONNECT, false)
    }
    
    /**
     * Set auto-connect setting
     */
    fun setAutoConnect(enabled: Boolean) {
        prefs.edit()
            .putBoolean(KEY_AUTO_CONNECT, enabled)
            .apply()
    }
    
    /**
     * Get bypass LAN setting
     */
    fun isBypassLanEnabled(): Boolean {
        return prefs.getBoolean(KEY_BYPASS_LAN, true)
    }
    
    /**
     * Set bypass LAN setting
     */
    fun setBypassLan(enabled: Boolean) {
        prefs.edit()
            .putBoolean(KEY_BYPASS_LAN, enabled)
            .apply()
    }
    
    /**
     * Get custom DNS servers
     */
    fun getDnsServers(): List<String> {
        val dnsString = prefs.getString(KEY_DNS_SERVERS, "8.8.8.8,8.8.4.4") ?: "8.8.8.8,8.8.4.4"
        return dnsString.split(",").map { it.trim() }.filter { it.isNotEmpty() }
    }
    
    /**
     * Set custom DNS servers
     */
    fun setDnsServers(dnsServers: List<String>) {
        val dnsString = dnsServers.joinToString(",")
        prefs.edit()
            .putString(KEY_DNS_SERVERS, dnsString)
            .apply()
    }
    
    /**
     * Validate server configuration
     */
    fun validateConfig(config: VlessConfig): List<String> {
        val errors = mutableListOf<String>()
        
        if (config.name.isBlank()) {
            errors.add("Server name is required")
        }
        
        if (config.address.isBlank()) {
            errors.add("Server address is required")
        }
        
        if (config.port !in 1..65535) {
            errors.add("Port must be between 1 and 65535")
        }
        
        if (config.uuid.isBlank()) {
            errors.add("UUID is required")
        } else {
            // Validate UUID format
            val uuidRegex = Regex("^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$")
            if (!config.uuid.matches(uuidRegex)) {
                errors.add("Invalid UUID format")
            }
        }
        
        if (config.network !in listOf("tcp", "ws", "grpc", "h2")) {
            errors.add("Unsupported network type")
        }
        
        if (config.security !in listOf("none", "tls", "reality")) {
            errors.add("Unsupported security type")
        }
        
        // Network-specific validations
        when (config.network) {
            "ws" -> {
                if (config.path.isNullOrBlank()) {
                    errors.add("WebSocket path is required")
                }
            }
            "grpc" -> {
                if (config.serviceName.isNullOrBlank()) {
                    errors.add("gRPC service name is required")
                }
            }
        }
        
        // Security-specific validations
        when (config.security) {
            "reality" -> {
                if (config.publicKey.isNullOrBlank()) {
                    errors.add("Reality public key is required")
                }
                if (config.shortId.isNullOrBlank()) {
                    errors.add("Reality short ID is required")
                }
            }
        }
        
        return errors
    }
    
    /**
     * Test server connectivity
     */
    suspend fun testServerConnectivity(config: VlessConfig): Boolean {
        return try {
            // This is a simplified connectivity test
            // In a real implementation, you would attempt to connect to the server
            val socket = java.net.Socket()
            socket.connect(java.net.InetSocketAddress(config.address, config.port), 5000)
            socket.close()
            true
        } catch (e: Exception) {
            false
        }
    }
    
    private fun saveServers(servers: List<VlessConfig>) {
        val serversJson = gson.toJson(servers)
        prefs.edit()
            .putString(KEY_SERVERS, serversJson)
            .apply()
    }
}
