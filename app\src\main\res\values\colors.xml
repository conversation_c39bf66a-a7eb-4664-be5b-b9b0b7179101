<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Primary colors -->
    <color name="purple_200">#FFBB86FC</color>
    <color name="purple_500">#FF6200EE</color>
    <color name="purple_700">#FF3700B3</color>
    <color name="teal_200">#FF03DAC5</color>
    <color name="teal_700">#FF018786</color>
    <color name="black">#FF000000</color>
    <color name="white">#FFFFFFFF</color>
    
    <!-- VPN specific colors -->
    <color name="vpn_primary">#FF2196F3</color>
    <color name="vpn_primary_dark">#FF1976D2</color>
    <color name="vpn_accent">#FF4CAF50</color>
    
    <!-- Status colors -->
    <color name="status_connected">#FF4CAF50</color>
    <color name="status_connecting">#FFFF9800</color>
    <color name="status_disconnected">#FFF44336</color>
    <color name="status_error">#FFE91E63</color>
    
    <!-- Background colors -->
    <color name="background_primary">#FFFAFAFA</color>
    <color name="background_secondary">#FFFFFFFF</color>
    <color name="background_card">#FFFFFFFF</color>
    <color name="background_dark">#FF121212</color>
    <color name="background_dark_secondary">#FF1E1E1E</color>
    
    <!-- Text colors -->
    <color name="text_primary">#DE000000</color>
    <color name="text_secondary">#99000000</color>
    <color name="text_hint">#61000000</color>
    <color name="text_primary_dark">#FFFFFFFF</color>
    <color name="text_secondary_dark">#B3FFFFFF</color>
    
    <!-- Chart colors -->
    <color name="chart_upload">#FF2196F3</color>
    <color name="chart_download">#FF4CAF50</color>
    <color name="chart_grid">#FFE0E0E0</color>
    
    <!-- Button colors -->
    <color name="button_connect">#FF4CAF50</color>
    <color name="button_disconnect">#FFF44336</color>
    <color name="button_disabled">#FFBDBDBD</color>
    
    <!-- Gradient colors -->
    <color name="gradient_start">#FF2196F3</color>
    <color name="gradient_end">#FF21CBF3</color>
    
    <!-- Ripple effect -->
    <color name="ripple_light">#1F000000</color>
    <color name="ripple_dark">#1FFFFFFF</color>
</resources>
