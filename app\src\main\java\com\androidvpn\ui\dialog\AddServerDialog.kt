package com.androidvpn.ui.dialog

import android.app.Dialog
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ArrayAdapter
import android.widget.Toast
import androidx.fragment.app.DialogFragment
import com.androidvpn.R
import com.androidvpn.databinding.DialogAddServerBinding
import com.androidvpn.model.VlessConfig
import com.google.android.material.dialog.MaterialAlertDialogBuilder
import java.util.*

class AddServerDialog(
    private val existingConfig: VlessConfig? = null,
    private val onSave: (VlessConfig) -> Unit
) : DialogFragment() {
    
    private var _binding: DialogAddServerBinding? = null
    private val binding get() = _binding!!
    
    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        _binding = DialogAddServerBinding.inflate(layoutInflater)
        
        setupUI()
        setupSpinners()
        setupClickListeners()
        
        if (existingConfig != null) {
            populateFields(existingConfig)
            binding.tvDialogTitle.text = getString(R.string.edit_server)
        }
        
        return MaterialAlertDialogBuilder(requireContext())
            .setView(binding.root)
            .create()
    }
    
    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
    
    private fun setupUI() {
        // Set default values
        binding.etServerPort.setText("443")
    }
    
    private fun setupSpinners() {
        // Network type spinner
        val networkTypes = arrayOf("TCP", "WebSocket", "gRPC", "HTTP/2")
        val networkAdapter = ArrayAdapter(requireContext(), android.R.layout.simple_dropdown_item_1line, networkTypes)
        binding.spinnerNetwork.setAdapter(networkAdapter)
        binding.spinnerNetwork.setText("TCP", false)
        
        // Security type spinner
        val securityTypes = arrayOf("None", "TLS", "Reality")
        val securityAdapter = ArrayAdapter(requireContext(), android.R.layout.simple_dropdown_item_1line, securityTypes)
        binding.spinnerSecurity.setAdapter(securityAdapter)
        binding.spinnerSecurity.setText("None", false)
        
        // Network type change listener
        binding.spinnerNetwork.setOnItemClickListener { _, _, position, _ ->
            updateAdvancedFields(networkTypes[position])
        }
        
        // Security type change listener
        binding.spinnerSecurity.setOnItemClickListener { _, _, position, _ ->
            updateSecurityFields(securityTypes[position])
        }
    }
    
    private fun setupClickListeners() {
        binding.btnCancel.setOnClickListener {
            dismiss()
        }
        
        binding.btnSave.setOnClickListener {
            saveServer()
        }
    }
    
    private fun updateAdvancedFields(networkType: String) {
        // Hide all advanced fields first
        binding.layoutWsPath.visibility = View.GONE
        binding.layoutHost.visibility = View.GONE
        
        // Show relevant fields based on network type
        when (networkType) {
            "WebSocket" -> {
                binding.layoutWsPath.visibility = View.VISIBLE
                binding.layoutHost.visibility = View.VISIBLE
                binding.etWsPath.setText("/")
            }
            "HTTP/2" -> {
                binding.layoutWsPath.visibility = View.VISIBLE
                binding.layoutHost.visibility = View.VISIBLE
                binding.etWsPath.setText("/")
            }
        }
    }
    
    private fun updateSecurityFields(securityType: String) {
        // Hide all security fields first
        binding.layoutSni.visibility = View.GONE
        binding.cbAllowInsecure.visibility = View.GONE
        
        // Show relevant fields based on security type
        when (securityType) {
            "TLS" -> {
                binding.layoutSni.visibility = View.VISIBLE
                binding.cbAllowInsecure.visibility = View.VISIBLE
            }
            "Reality" -> {
                binding.layoutSni.visibility = View.VISIBLE
            }
        }
    }
    
    private fun populateFields(config: VlessConfig) {
        binding.etServerName.setText(config.name)
        binding.etServerAddress.setText(config.address)
        binding.etServerPort.setText(config.port.toString())
        binding.etUuid.setText(config.uuid)
        
        // Set network type
        val networkType = when (config.network) {
            "tcp" -> "TCP"
            "ws" -> "WebSocket"
            "grpc" -> "gRPC"
            "h2" -> "HTTP/2"
            else -> "TCP"
        }
        binding.spinnerNetwork.setText(networkType, false)
        updateAdvancedFields(networkType)
        
        // Set security type
        val securityType = when (config.security) {
            "none" -> "None"
            "tls" -> "TLS"
            "reality" -> "Reality"
            else -> "None"
        }
        binding.spinnerSecurity.setText(securityType, false)
        updateSecurityFields(securityType)
        
        // Set advanced fields
        binding.etWsPath.setText(config.path ?: "")
        binding.etHost.setText(config.host ?: "")
        binding.etSni.setText(config.sni ?: "")
        binding.cbAllowInsecure.isChecked = config.allowInsecure
    }
    
    private fun saveServer() {
        val name = binding.etServerName.text.toString().trim()
        val address = binding.etServerAddress.text.toString().trim()
        val portText = binding.etServerPort.text.toString().trim()
        val uuid = binding.etUuid.text.toString().trim()
        val networkType = binding.spinnerNetwork.text.toString()
        val securityType = binding.spinnerSecurity.text.toString()
        
        // Validation
        if (name.isEmpty()) {
            binding.etServerName.error = "Server name is required"
            return
        }
        
        if (address.isEmpty()) {
            binding.etServerAddress.error = "Server address is required"
            return
        }
        
        val port = portText.toIntOrNull()
        if (port == null || port !in 1..65535) {
            binding.etServerPort.error = "Invalid port number"
            return
        }
        
        if (uuid.isEmpty()) {
            binding.etUuid.error = "UUID is required"
            return
        }
        
        // Validate UUID format
        val uuidRegex = Regex("^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$")
        if (!uuid.matches(uuidRegex)) {
            binding.etUuid.error = "Invalid UUID format"
            return
        }
        
        // Convert UI values to config values
        val network = when (networkType) {
            "TCP" -> "tcp"
            "WebSocket" -> "ws"
            "gRPC" -> "grpc"
            "HTTP/2" -> "h2"
            else -> "tcp"
        }
        
        val security = when (securityType) {
            "None" -> "none"
            "TLS" -> "tls"
            "Reality" -> "reality"
            else -> "none"
        }
        
        // Get advanced fields
        val path = binding.etWsPath.text.toString().trim().takeIf { it.isNotEmpty() }
        val host = binding.etHost.text.toString().trim().takeIf { it.isNotEmpty() }
        val sni = binding.etSni.text.toString().trim().takeIf { it.isNotEmpty() }
        val allowInsecure = binding.cbAllowInsecure.isChecked
        
        // Create config
        val config = VlessConfig(
            id = existingConfig?.id ?: UUID.randomUUID().toString(),
            name = name,
            address = address,
            port = port,
            uuid = uuid,
            security = security,
            network = network,
            path = path,
            host = host,
            sni = sni,
            allowInsecure = allowInsecure,
            createdAt = existingConfig?.createdAt ?: System.currentTimeMillis()
        )
        
        onSave(config)
        dismiss()
    }
}
