<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="4dp"
    android:clickable="true"
    android:focusable="true"
    app:cardCornerRadius="12dp"
    app:cardElevation="2dp"
    app:cardBackgroundColor="@color/background_card"
    app:rippleColor="@color/ripple_light">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp"
        android:gravity="center_vertical">

        <!-- Server Icon with animated GIF -->
        <FrameLayout
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_marginEnd="16dp">

            <pl.droidsonroids.gif.GifImageView
                android:id="@+id/gifServerStatus"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:src="@drawable/server_idle_animation"
                android:scaleType="centerCrop" />

            <!-- Connection indicator -->
            <View
                android:id="@+id/viewConnectionIndicator"
                android:layout_width="12dp"
                android:layout_height="12dp"
                android:layout_gravity="bottom|end"
                android:background="@drawable/circle_indicator"
                android:backgroundTint="@color/status_disconnected" />

        </FrameLayout>

        <!-- Server Info -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tvServerName"
                style="@style/ServerName"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:maxLines="1"
                android:ellipsize="end"
                tools:text="My VPN Server" />

            <TextView
                android:id="@+id/tvServerAddress"
                style="@style/ServerAddress"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:maxLines="1"
                android:ellipsize="end"
                tools:text="example.com:443" />

            <!-- Server details -->
            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:orientation="horizontal">

                <com.google.android.material.chip.Chip
                    android:id="@+id/chipNetwork"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="4dp"
                    android:textSize="10sp"
                    app:chipMinHeight="24dp"
                    app:chipBackgroundColor="@color/vpn_primary"
                    app:chipStrokeWidth="0dp"
                    tools:text="WebSocket" />

                <com.google.android.material.chip.Chip
                    android:id="@+id/chipSecurity"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textSize="10sp"
                    app:chipMinHeight="24dp"
                    app:chipBackgroundColor="@color/status_connected"
                    app:chipStrokeWidth="0dp"
                    tools:text="TLS" />

            </LinearLayout>

        </LinearLayout>

        <!-- Ping indicator -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="center"
            android:layout_marginStart="8dp">

            <TextView
                android:id="@+id/tvPing"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="12sp"
                android:textStyle="bold"
                android:textColor="@color/status_connected"
                tools:text="45ms" />

            <!-- Signal strength bars -->
            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:orientation="horizontal"
                android:gravity="bottom">

                <View
                    android:id="@+id/signalBar1"
                    android:layout_width="3dp"
                    android:layout_height="6dp"
                    android:layout_marginEnd="1dp"
                    android:background="@color/status_connected" />

                <View
                    android:id="@+id/signalBar2"
                    android:layout_width="3dp"
                    android:layout_height="9dp"
                    android:layout_marginEnd="1dp"
                    android:background="@color/status_connected" />

                <View
                    android:id="@+id/signalBar3"
                    android:layout_width="3dp"
                    android:layout_height="12dp"
                    android:layout_marginEnd="1dp"
                    android:background="@color/status_connected" />

                <View
                    android:id="@+id/signalBar4"
                    android:layout_width="3dp"
                    android:layout_height="15dp"
                    android:background="@color/status_connecting" />

            </LinearLayout>

        </LinearLayout>

        <!-- Menu button -->
        <com.google.android.material.button.MaterialButton
            android:id="@+id/btnServerMenu"
            style="@style/Widget.Material3.Button.IconButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            app:icon="@drawable/ic_more_vert"
            app:iconTint="@color/text_secondary" />

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
