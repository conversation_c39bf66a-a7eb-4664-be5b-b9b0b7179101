package com.androidvpn.model

/**
 * Data class representing connection statistics
 */
data class ConnectionStats(
    val uploadSpeed: Long = 0L,           // bytes per second
    val downloadSpeed: Long = 0L,         // bytes per second
    val totalUpload: Long = 0L,           // total bytes uploaded
    val totalDownload: Long = 0L,         // total bytes downloaded
    val connectionTime: Long = 0L,        // connection duration in milliseconds
    val ping: Int = -1,                   // ping in milliseconds, -1 if unknown
    val packetLoss: Double = 0.0,         // packet loss percentage
    val serverLocation: String = "",      // server location/country
    val localIP: String = "",             // local VPN IP address
    val serverIP: String = "",            // server IP address
    val protocol: String = "",            // connection protocol (VLESS)
    val encryption: String = "",          // encryption method
    val isConnected: Boolean = false      // connection status
) {
    
    /**
     * Get upload speed in a human-readable format
     */
    fun getFormattedUploadSpeed(): String {
        return formatSpeed(uploadSpeed)
    }
    
    /**
     * Get download speed in a human-readable format
     */
    fun getFormattedDownloadSpeed(): String {
        return formatSpeed(downloadSpeed)
    }
    
    /**
     * Get total upload in a human-readable format
     */
    fun getFormattedTotalUpload(): String {
        return formatBytes(totalUpload)
    }
    
    /**
     * Get total download in a human-readable format
     */
    fun getFormattedTotalDownload(): String {
        return formatBytes(totalDownload)
    }
    
    /**
     * Get connection time in a human-readable format
     */
    fun getFormattedConnectionTime(): String {
        return formatDuration(connectionTime)
    }
    
    /**
     * Get ping in a human-readable format
     */
    fun getFormattedPing(): String {
        return if (ping >= 0) "${ping}ms" else "N/A"
    }
    
    /**
     * Get packet loss in a human-readable format
     */
    fun getFormattedPacketLoss(): String {
        return "${String.format("%.1f", packetLoss)}%"
    }
    
    /**
     * Get connection quality based on ping and packet loss
     */
    fun getConnectionQuality(): ConnectionQuality {
        return when {
            !isConnected -> ConnectionQuality.DISCONNECTED
            ping < 0 -> ConnectionQuality.UNKNOWN
            ping < 50 && packetLoss < 1.0 -> ConnectionQuality.EXCELLENT
            ping < 100 && packetLoss < 2.0 -> ConnectionQuality.GOOD
            ping < 200 && packetLoss < 5.0 -> ConnectionQuality.FAIR
            else -> ConnectionQuality.POOR
        }
    }
    
    /**
     * Get total data transferred (upload + download)
     */
    fun getTotalDataTransferred(): Long {
        return totalUpload + totalDownload
    }
    
    /**
     * Get formatted total data transferred
     */
    fun getFormattedTotalDataTransferred(): String {
        return formatBytes(getTotalDataTransferred())
    }
    
    /**
     * Get average speed (upload + download)
     */
    fun getAverageSpeed(): Long {
        return (uploadSpeed + downloadSpeed) / 2
    }
    
    /**
     * Get formatted average speed
     */
    fun getFormattedAverageSpeed(): String {
        return formatSpeed(getAverageSpeed())
    }
    
    private fun formatSpeed(bytesPerSecond: Long): String {
        return when {
            bytesPerSecond < 1024 -> "${bytesPerSecond} B/s"
            bytesPerSecond < 1024 * 1024 -> "${String.format("%.1f", bytesPerSecond / 1024.0)} KB/s"
            bytesPerSecond < 1024 * 1024 * 1024 -> "${String.format("%.1f", bytesPerSecond / (1024.0 * 1024.0))} MB/s"
            else -> "${String.format("%.1f", bytesPerSecond / (1024.0 * 1024.0 * 1024.0))} GB/s"
        }
    }
    
    private fun formatBytes(bytes: Long): String {
        return when {
            bytes < 1024 -> "$bytes B"
            bytes < 1024 * 1024 -> "${String.format("%.1f", bytes / 1024.0)} KB"
            bytes < 1024 * 1024 * 1024 -> "${String.format("%.1f", bytes / (1024.0 * 1024.0))} MB"
            else -> "${String.format("%.1f", bytes / (1024.0 * 1024.0 * 1024.0))} GB"
        }
    }
    
    private fun formatDuration(durationMs: Long): String {
        val seconds = durationMs / 1000
        val minutes = seconds / 60
        val hours = minutes / 60
        
        return when {
            hours > 0 -> String.format("%02d:%02d:%02d", hours, minutes % 60, seconds % 60)
            minutes > 0 -> String.format("%02d:%02d", minutes, seconds % 60)
            else -> String.format("00:%02d", seconds)
        }
    }
}

/**
 * Enum representing connection quality levels
 */
enum class ConnectionQuality {
    EXCELLENT,
    GOOD,
    FAIR,
    POOR,
    UNKNOWN,
    DISCONNECTED;
    
    fun getDisplayName(): String {
        return when (this) {
            EXCELLENT -> "Excellent"
            GOOD -> "Good"
            FAIR -> "Fair"
            POOR -> "Poor"
            UNKNOWN -> "Unknown"
            DISCONNECTED -> "Disconnected"
        }
    }
    
    fun getColorResource(): Int {
        return when (this) {
            EXCELLENT -> android.R.color.holo_green_dark
            GOOD -> android.R.color.holo_green_light
            FAIR -> android.R.color.holo_orange_light
            POOR -> android.R.color.holo_red_light
            UNKNOWN -> android.R.color.darker_gray
            DISCONNECTED -> android.R.color.darker_gray
        }
    }
}
