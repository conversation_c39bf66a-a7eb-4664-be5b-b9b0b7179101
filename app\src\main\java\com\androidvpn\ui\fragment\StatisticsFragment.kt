package com.androidvpn.ui.fragment

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import com.androidvpn.R
import com.androidvpn.databinding.FragmentStatisticsBinding
import com.androidvpn.model.ConnectionStats
import com.androidvpn.utils.FormatUtils
import com.androidvpn.utils.StatisticsMonitor
import com.github.mikephil.charting.components.XAxis
import com.github.mikephil.charting.data.Entry
import com.github.mikephil.charting.data.LineData
import com.github.mikephil.charting.data.LineDataSet
import kotlinx.coroutines.launch

class StatisticsFragment : Fragment() {
    
    private var _binding: FragmentStatisticsBinding? = null
    private val binding get() = _binding!!
    
    private var statisticsMonitor: StatisticsMonitor? = null
    private val speedEntries = mutableListOf<Entry>()
    private val uploadEntries = mutableListOf<Entry>()
    private var entryIndex = 0f
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentStatisticsBinding.inflate(inflater, container, false)
        return binding.root
    }
    
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        setupChart()
        startMonitoring()
    }
    
    override fun onDestroyView() {
        super.onDestroyView()
        statisticsMonitor?.cleanup()
        _binding = null
    }
    
    private fun setupChart() {
        binding.chartRealTimeSpeed.apply {
            description.isEnabled = false
            setTouchEnabled(true)
            isDragEnabled = true
            setScaleEnabled(true)
            setPinchZoom(true)
            
            xAxis.apply {
                position = XAxis.XAxisPosition.BOTTOM
                setDrawGridLines(false)
                granularity = 1f
                setLabelCount(6, false)
            }
            
            axisLeft.apply {
                setDrawGridLines(true)
                axisMinimum = 0f
                textColor = ContextCompat.getColor(requireContext(), R.color.text_secondary)
            }
            
            axisRight.isEnabled = false
            legend.apply {
                isEnabled = true
                textColor = ContextCompat.getColor(requireContext(), R.color.text_primary)
            }
        }
    }
    
    private fun startMonitoring() {
        statisticsMonitor = StatisticsMonitor(requireContext())
        
        // Start monitoring with mock server data
        statisticsMonitor?.startMonitoring("example.com", 443)
        
        // Observe statistics updates
        lifecycleScope.launch {
            statisticsMonitor?.statistics?.collect { stats ->
                updateUI(stats)
                updateChart(stats)
            }
        }
    }
    
    private fun updateUI(stats: ConnectionStats) {
        binding.apply {
            // Update current speeds
            tvCurrentDownloadSpeed.text = FormatUtils.formatSpeed(stats.downloadSpeed)
            tvCurrentUploadSpeed.text = FormatUtils.formatSpeed(stats.uploadSpeed)
            
            // Update data usage
            val totalUsage = stats.totalUpload + stats.totalDownload
            tvSessionUsage.text = FormatUtils.formatBytes(totalUsage)
            
            // Mock today and monthly usage (in real app, these would be tracked separately)
            val todayUsage = totalUsage * 2 // Mock multiplier
            val monthlyUsage = totalUsage * 30 // Mock multiplier
            
            tvTodayUsage.text = FormatUtils.formatBytes(todayUsage)
            tvMonthlyUsage.text = FormatUtils.formatBytes(monthlyUsage)
            
            // Update connection quality
            tvPingValue.text = if (stats.ping >= 0) "${stats.ping}" else "--"
            tvQualityValue.text = stats.getConnectionQuality().getDisplayName()
            tvUptimeValue.text = FormatUtils.formatDurationShort(stats.connectionTime)
            
            // Update ping color based on value
            val pingColor = when {
                stats.ping < 0 -> R.color.text_secondary
                stats.ping < 50 -> R.color.status_connected
                stats.ping < 100 -> R.color.status_connecting
                else -> R.color.status_error
            }
            tvPingValue.setTextColor(ContextCompat.getColor(requireContext(), pingColor))
            
            // Update quality color
            val qualityColor = when (stats.getConnectionQuality()) {
                com.androidvpn.model.ConnectionQuality.EXCELLENT -> R.color.status_connected
                com.androidvpn.model.ConnectionQuality.GOOD -> R.color.status_connected
                com.androidvpn.model.ConnectionQuality.FAIR -> R.color.status_connecting
                com.androidvpn.model.ConnectionQuality.POOR -> R.color.status_error
                else -> R.color.text_secondary
            }
            tvQualityValue.setTextColor(ContextCompat.getColor(requireContext(), qualityColor))
        }
    }
    
    private fun updateChart(stats: ConnectionStats) {
        // Add new data points
        speedEntries.add(Entry(entryIndex, (stats.downloadSpeed / 1024f))) // Convert to KB/s
        uploadEntries.add(Entry(entryIndex, (stats.uploadSpeed / 1024f))) // Convert to KB/s
        entryIndex++
        
        // Keep only last 60 entries (1 minute of data)
        if (speedEntries.size > 60) {
            speedEntries.removeAt(0)
            uploadEntries.removeAt(0)
            
            // Adjust x values
            speedEntries.forEachIndexed { index, entry ->
                entry.x = index.toFloat()
            }
            uploadEntries.forEachIndexed { index, entry ->
                entry.x = index.toFloat()
            }
            entryIndex = speedEntries.size.toFloat()
        }
        
        // Create datasets
        val downloadDataSet = LineDataSet(speedEntries, "Download").apply {
            color = ContextCompat.getColor(requireContext(), R.color.status_connected)
            setCircleColor(ContextCompat.getColor(requireContext(), R.color.status_connected))
            lineWidth = 2f
            circleRadius = 2f
            setDrawCircleHole(false)
            valueTextSize = 0f
            setDrawFilled(true)
            fillColor = ContextCompat.getColor(requireContext(), R.color.status_connected)
            fillAlpha = 50
        }
        
        val uploadDataSet = LineDataSet(uploadEntries, "Upload").apply {
            color = ContextCompat.getColor(requireContext(), R.color.vpn_primary)
            setCircleColor(ContextCompat.getColor(requireContext(), R.color.vpn_primary))
            lineWidth = 2f
            circleRadius = 2f
            setDrawCircleHole(false)
            valueTextSize = 0f
            setDrawFilled(true)
            fillColor = ContextCompat.getColor(requireContext(), R.color.vpn_primary)
            fillAlpha = 50
        }
        
        val lineData = LineData(downloadDataSet, uploadDataSet)
        binding.chartRealTimeSpeed.data = lineData
        binding.chartRealTimeSpeed.invalidate()
    }
    
    companion object {
        fun newInstance(): StatisticsFragment {
            return StatisticsFragment()
        }
    }
}
