<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/gradient_background"
    tools:context=".ui.MainActivity">

    <!-- App Bar -->
    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar"
        app:elevation="0dp">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@android:color/transparent"
            app:title="@string/app_name"
            app:titleTextColor="@color/white" />

    </com.google.android.material.appbar.AppBarLayout>

    <!-- Main Content -->
    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- Connection Status Card -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="16dp"
                app:cardElevation="8dp"
                app:cardBackgroundColor="@color/white">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="24dp"
                    android:gravity="center">

                    <!-- Animated GIF for connection status -->
                    <pl.droidsonroids.gif.GifImageView
                        android:id="@+id/gifConnectionStatus"
                        android:layout_width="120dp"
                        android:layout_height="120dp"
                        android:layout_marginBottom="16dp"
                        android:src="@drawable/vpn_disconnected_animation" />

                    <!-- Connection Button -->
                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btnConnect"
                        style="@style/ConnectButton.Disconnected"
                        android:layout_width="180dp"
                        android:layout_height="180dp"
                        android:text="@string/connect"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        app:cornerRadius="90dp"
                        app:elevation="12dp" />

                    <!-- Status Text -->
                    <TextView
                        android:id="@+id/tvConnectionStatus"
                        style="@style/StatusText"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/disconnected"
                        android:textColor="@color/status_disconnected" />

                    <!-- Server Name -->
                    <TextView
                        android:id="@+id/tvServerName"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:textSize="16sp"
                        android:textColor="@color/text_secondary"
                        android:visibility="gone"
                        tools:text="My VPN Server"
                        tools:visibility="visible" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Statistics Card -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/cardStatistics"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:visibility="gone"
                app:cardCornerRadius="16dp"
                app:cardElevation="4dp"
                app:cardBackgroundColor="@color/white"
                tools:visibility="visible">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Статистика"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="@color/text_primary"
                        android:layout_marginBottom="16dp" />

                    <!-- Speed Chart -->
                    <com.github.mikephil.charting.charts.LineChart
                        android:id="@+id/chartSpeed"
                        android:layout_width="match_parent"
                        android:layout_height="200dp"
                        android:layout_marginBottom="16dp" />

                    <!-- Statistics Grid -->
                    <GridLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:columnCount="2"
                        android:rowCount="3">

                        <!-- Upload Speed -->
                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_columnWeight="1"
                            android:orientation="vertical"
                            android:padding="8dp">

                            <TextView
                                style="@style/StatisticTitle"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/upload_speed" />

                            <TextView
                                android:id="@+id/tvUploadSpeed"
                                style="@style/StatisticValue"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                tools:text="1.2 МБ/с" />

                        </LinearLayout>

                        <!-- Download Speed -->
                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_columnWeight="1"
                            android:orientation="vertical"
                            android:padding="8dp">

                            <TextView
                                style="@style/StatisticTitle"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/download_speed" />

                            <TextView
                                android:id="@+id/tvDownloadSpeed"
                                style="@style/StatisticValue"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                tools:text="5.8 МБ/с" />

                        </LinearLayout>

                        <!-- Total Upload -->
                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_columnWeight="1"
                            android:orientation="vertical"
                            android:padding="8dp">

                            <TextView
                                style="@style/StatisticTitle"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/total_upload" />

                            <TextView
                                android:id="@+id/tvTotalUpload"
                                style="@style/StatisticValue"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                tools:text="125 МБ" />

                        </LinearLayout>

                        <!-- Total Download -->
                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_columnWeight="1"
                            android:orientation="vertical"
                            android:padding="8dp">

                            <TextView
                                style="@style/StatisticTitle"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/total_download" />

                            <TextView
                                android:id="@+id/tvTotalDownload"
                                style="@style/StatisticValue"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                tools:text="1.2 ГБ" />

                        </LinearLayout>

                        <!-- Connection Time -->
                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_columnWeight="1"
                            android:orientation="vertical"
                            android:padding="8dp">

                            <TextView
                                style="@style/StatisticTitle"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/connection_time" />

                            <TextView
                                android:id="@+id/tvConnectionTime"
                                style="@style/StatisticValue"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                tools:text="02:35:42" />

                        </LinearLayout>

                        <!-- Ping -->
                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_columnWeight="1"
                            android:orientation="vertical"
                            android:padding="8dp">

                            <TextView
                                style="@style/StatisticTitle"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/ping" />

                            <TextView
                                android:id="@+id/tvPing"
                                style="@style/StatisticValue"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                tools:text="45 мс" />

                        </LinearLayout>

                    </GridLayout>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Servers Card -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:cardCornerRadius="16dp"
                app:cardElevation="4dp"
                app:cardBackgroundColor="@color/white">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginBottom="16dp">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="Серверы"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            android:textColor="@color/text_primary" />

                        <com.google.android.material.button.MaterialButton
                            android:id="@+id/btnAddServer"
                            style="@style/Widget.Material3.Button.IconButton"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            app:icon="@drawable/ic_add"
                            app:iconTint="@color/vpn_primary" />

                        <com.google.android.material.button.MaterialButton
                            android:id="@+id/btnScanQr"
                            style="@style/Widget.Material3.Button.IconButton"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            app:icon="@drawable/ic_qr_code_scanner"
                            app:iconTint="@color/vpn_primary" />

                    </LinearLayout>

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/recyclerViewServers"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:nestedScrollingEnabled="false"
                        tools:listitem="@layout/item_server" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

    <!-- Floating Action Button -->
    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/fabSettings"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_margin="16dp"
        app:srcCompat="@drawable/ic_settings"
        app:tint="@color/white"
        app:backgroundTint="@color/vpn_primary" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>
