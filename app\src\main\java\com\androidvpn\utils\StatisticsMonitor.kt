package com.androidvpn.utils

import android.content.Context
import android.net.TrafficStats
import com.androidvpn.model.ConnectionStats
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import java.net.InetAddress
import java.util.concurrent.atomic.AtomicLong

/**
 * Monitor for tracking VPN connection statistics
 */
class StatisticsMonitor(private val context: Context) {
    
    private val scope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    
    private val _statistics = MutableStateFlow(ConnectionStats())
    val statistics: StateFlow<ConnectionStats> = _statistics.asStateFlow()
    
    private var isMonitoring = false
    private var monitoringJob: Job? = null
    
    private var connectionStartTime = 0L
    private var lastRxBytes = 0L
    private var lastTxBytes = 0L
    private var lastUpdateTime = 0L
    
    private val totalRxBytes = AtomicLong(0)
    private val totalTxBytes = AtomicLong(0)
    
    private var serverAddress: String = ""
    private var serverPort: Int = 0
    
    /**
     * Start monitoring statistics
     */
    fun startMonitoring(serverAddress: String, serverPort: Int) {
        if (isMonitoring) return
        
        this.serverAddress = serverAddress
        this.serverPort = serverPort
        
        isMonitoring = true
        connectionStartTime = System.currentTimeMillis()
        lastUpdateTime = connectionStartTime
        
        // Initialize traffic stats
        lastRxBytes = TrafficStats.getTotalRxBytes()
        lastTxBytes = TrafficStats.getTotalTxBytes()
        
        totalRxBytes.set(0)
        totalTxBytes.set(0)
        
        monitoringJob = scope.launch {
            while (isMonitoring) {
                updateStatistics()
                delay(1000) // Update every second
            }
        }
    }
    
    /**
     * Stop monitoring statistics
     */
    fun stopMonitoring() {
        isMonitoring = false
        monitoringJob?.cancel()
        monitoringJob = null
        
        // Reset statistics
        _statistics.value = ConnectionStats()
    }
    
    /**
     * Update statistics data
     */
    private suspend fun updateStatistics() {
        val currentTime = System.currentTimeMillis()
        val currentRxBytes = TrafficStats.getTotalRxBytes()
        val currentTxBytes = TrafficStats.getTotalTxBytes()
        
        if (lastRxBytes > 0 && lastTxBytes > 0) {
            val timeDiff = currentTime - lastUpdateTime
            val rxDiff = currentRxBytes - lastRxBytes
            val txDiff = currentTxBytes - lastTxBytes
            
            if (timeDiff > 0) {
                val downloadSpeed = (rxDiff * 1000) / timeDiff
                val uploadSpeed = (txDiff * 1000) / timeDiff
                
                totalRxBytes.addAndGet(rxDiff)
                totalTxBytes.addAndGet(txDiff)
                
                val connectionTime = currentTime - connectionStartTime
                val ping = measurePing()
                
                val stats = ConnectionStats(
                    uploadSpeed = uploadSpeed,
                    downloadSpeed = downloadSpeed,
                    totalUpload = totalTxBytes.get(),
                    totalDownload = totalRxBytes.get(),
                    connectionTime = connectionTime,
                    ping = ping,
                    packetLoss = 0.0, // TODO: Implement packet loss calculation
                    serverLocation = getServerLocation(),
                    localIP = getLocalIP(),
                    serverIP = serverAddress,
                    protocol = "VLESS",
                    encryption = "AES-256-GCM",
                    isConnected = true
                )
                
                _statistics.value = stats
            }
        }
        
        lastRxBytes = currentRxBytes
        lastTxBytes = currentTxBytes
        lastUpdateTime = currentTime
    }
    
    /**
     * Measure ping to server
     */
    private suspend fun measurePing(): Int {
        return withContext(Dispatchers.IO) {
            try {
                val startTime = System.currentTimeMillis()
                val address = InetAddress.getByName(serverAddress)
                val reachable = address.isReachable(5000)
                val endTime = System.currentTimeMillis()
                
                if (reachable) {
                    (endTime - startTime).toInt()
                } else {
                    -1
                }
            } catch (e: Exception) {
                -1
            }
        }
    }
    
    /**
     * Get server location (mock implementation)
     */
    private fun getServerLocation(): String {
        // In a real implementation, you would use a GeoIP service
        return when {
            serverAddress.contains("us") -> "United States"
            serverAddress.contains("eu") -> "Europe"
            serverAddress.contains("asia") -> "Asia"
            else -> "Unknown"
        }
    }
    
    /**
     * Get local VPN IP address
     */
    private fun getLocalIP(): String {
        // This would typically be the VPN interface IP
        return "********" // Mock IP
    }
    
    /**
     * Get current statistics snapshot
     */
    fun getCurrentStats(): ConnectionStats {
        return _statistics.value
    }
    
    /**
     * Reset statistics counters
     */
    fun resetCounters() {
        totalRxBytes.set(0)
        totalTxBytes.set(0)
        connectionStartTime = System.currentTimeMillis()
        lastUpdateTime = connectionStartTime
    }
    
    /**
     * Get data usage for today
     */
    fun getTodayDataUsage(): Pair<Long, Long> {
        // In a real implementation, you would track daily usage
        return Pair(totalTxBytes.get(), totalRxBytes.get())
    }
    
    /**
     * Get data usage for this month
     */
    fun getMonthlyDataUsage(): Pair<Long, Long> {
        // In a real implementation, you would track monthly usage
        return Pair(totalTxBytes.get() * 30, totalRxBytes.get() * 30)
    }
    
    /**
     * Check if monitoring is active
     */
    fun isMonitoring(): Boolean {
        return isMonitoring
    }
    
    /**
     * Get connection uptime
     */
    fun getUptime(): Long {
        return if (isMonitoring) {
            System.currentTimeMillis() - connectionStartTime
        } else {
            0L
        }
    }
    
    /**
     * Get average speeds since connection started
     */
    fun getAverageSpeeds(): Pair<Long, Long> {
        val uptime = getUptime()
        return if (uptime > 0) {
            val avgUpload = (totalTxBytes.get() * 1000) / uptime
            val avgDownload = (totalRxBytes.get() * 1000) / uptime
            Pair(avgUpload, avgDownload)
        } else {
            Pair(0L, 0L)
        }
    }
    
    /**
     * Cleanup resources
     */
    fun cleanup() {
        stopMonitoring()
        scope.cancel()
    }
}
