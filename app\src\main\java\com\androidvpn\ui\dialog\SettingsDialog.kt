package com.androidvpn.ui.dialog

import android.app.Dialog
import android.content.Intent
import android.os.Bundle
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.fragment.app.DialogFragment
import com.androidvpn.R
import com.androidvpn.databinding.DialogSettingsBinding
import com.androidvpn.utils.ConfigManager
import com.google.android.material.dialog.MaterialAlertDialogBuilder
import java.io.BufferedReader
import java.io.InputStreamReader

class SettingsDialog : DialogFragment() {
    
    private var _binding: DialogSettingsBinding? = null
    private val binding get() = _binding!!
    
    private lateinit var configManager: ConfigManager
    
    // File picker for import
    private val importLauncher = registerForActivityResult(
        ActivityResultContracts.GetContent()
    ) { uri ->
        uri?.let { importServers(it) }
    }
    
    // File picker for export
    private val exportLauncher = registerForActivityResult(
        ActivityResultContracts.CreateDocument("application/json")
    ) { uri ->
        uri?.let { exportServers(it) }
    }
    
    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        _binding = DialogSettingsBinding.inflate(layoutInflater)
        configManager = ConfigManager(requireContext())
        
        setupUI()
        setupClickListeners()
        loadSettings()
        
        return MaterialAlertDialogBuilder(requireContext())
            .setView(binding.root)
            .create()
    }
    
    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
    
    private fun setupUI() {
        // Setup slider listener
        binding.sliderUpdateInterval.addOnChangeListener { _, value, _ ->
            binding.tvUpdateInterval.text = "${value.toInt()} seconds"
        }
    }
    
    private fun setupClickListeners() {
        binding.btnCancel.setOnClickListener {
            dismiss()
        }
        
        binding.btnSave.setOnClickListener {
            saveSettings()
        }
        
        binding.btnExportServers.setOnClickListener {
            exportLauncher.launch("vpn_servers.json")
        }
        
        binding.btnImportServers.setOnClickListener {
            importLauncher.launch("application/json")
        }
        
        binding.btnClearData.setOnClickListener {
            showClearDataConfirmation()
        }
    }
    
    private fun loadSettings() {
        // Load connection settings
        binding.cbAutoConnect.isChecked = configManager.isAutoConnectEnabled()
        binding.cbBypassLan.isChecked = configManager.isBypassLanEnabled()
        binding.etDnsServers.setText(configManager.getDnsServers().joinToString(","))
        
        // Load app settings from SharedPreferences
        val prefs = requireContext().getSharedPreferences("app_settings", 0)
        binding.cbDarkTheme.isChecked = prefs.getBoolean("dark_theme", false)
        binding.cbShowNotifications.isChecked = prefs.getBoolean("show_notifications", true)
        
        val updateInterval = prefs.getInt("update_interval", 3)
        binding.sliderUpdateInterval.value = updateInterval.toFloat()
        binding.tvUpdateInterval.text = "$updateInterval seconds"
    }
    
    private fun saveSettings() {
        // Save connection settings
        configManager.setAutoConnect(binding.cbAutoConnect.isChecked)
        configManager.setBypassLan(binding.cbBypassLan.isChecked)
        
        val dnsServers = binding.etDnsServers.text.toString()
            .split(",")
            .map { it.trim() }
            .filter { it.isNotEmpty() }
        configManager.setDnsServers(dnsServers)
        
        // Save app settings
        val prefs = requireContext().getSharedPreferences("app_settings", 0)
        prefs.edit()
            .putBoolean("dark_theme", binding.cbDarkTheme.isChecked)
            .putBoolean("show_notifications", binding.cbShowNotifications.isChecked)
            .putInt("update_interval", binding.sliderUpdateInterval.value.toInt())
            .apply()
        
        Toast.makeText(requireContext(), "Settings saved", Toast.LENGTH_SHORT).show()
        dismiss()
    }
    
    private fun exportServers(uri: android.net.Uri) {
        try {
            val serversJson = configManager.exportServers()
            
            requireContext().contentResolver.openOutputStream(uri)?.use { outputStream ->
                outputStream.write(serversJson.toByteArray())
            }
            
            Toast.makeText(requireContext(), "Servers exported successfully", Toast.LENGTH_SHORT).show()
        } catch (e: Exception) {
            Toast.makeText(requireContext(), "Failed to export servers: ${e.message}", Toast.LENGTH_LONG).show()
        }
    }
    
    private fun importServers(uri: android.net.Uri) {
        try {
            val inputStream = requireContext().contentResolver.openInputStream(uri)
            val reader = BufferedReader(InputStreamReader(inputStream))
            val jsonString = reader.readText()
            reader.close()
            
            val success = configManager.importServers(jsonString)
            
            if (success) {
                Toast.makeText(requireContext(), "Servers imported successfully", Toast.LENGTH_SHORT).show()
            } else {
                Toast.makeText(requireContext(), "Failed to import servers: Invalid format", Toast.LENGTH_LONG).show()
            }
        } catch (e: Exception) {
            Toast.makeText(requireContext(), "Failed to import servers: ${e.message}", Toast.LENGTH_LONG).show()
        }
    }
    
    private fun showClearDataConfirmation() {
        MaterialAlertDialogBuilder(requireContext())
            .setTitle("Clear All Data")
            .setMessage("This will delete all server configurations and settings. This action cannot be undone.")
            .setPositiveButton("Clear") { _, _ ->
                clearAllData()
            }
            .setNegativeButton("Cancel", null)
            .show()
    }
    
    private fun clearAllData() {
        try {
            // Clear server configurations
            val servers = configManager.getAllServers()
            servers.forEach { server ->
                configManager.deleteServer(server)
            }
            
            // Clear app settings
            val prefs = requireContext().getSharedPreferences("app_settings", 0)
            prefs.edit().clear().apply()
            
            // Clear config manager settings
            val configPrefs = requireContext().getSharedPreferences("vpn_configs", 0)
            configPrefs.edit().clear().apply()
            
            Toast.makeText(requireContext(), "All data cleared", Toast.LENGTH_SHORT).show()
            dismiss()
        } catch (e: Exception) {
            Toast.makeText(requireContext(), "Failed to clear data: ${e.message}", Toast.LENGTH_LONG).show()
        }
    }
}
