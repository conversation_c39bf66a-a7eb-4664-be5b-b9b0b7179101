<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:padding="16dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- Title -->
        <TextView
            android:id="@+id/tvDialogTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/add_server"
            android:textSize="20sp"
            android:textStyle="bold"
            android:textColor="@color/text_primary"
            android:layout_marginBottom="16dp" />

        <!-- Server Name -->
        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="12dp"
            app:boxStrokeColor="@color/vpn_primary"
            app:hintTextColor="@color/vpn_primary">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/etServerName"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="@string/server_name"
                android:inputType="text"
                android:maxLines="1" />

        </com.google.android.material.textfield.TextInputLayout>

        <!-- Server Address -->
        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="12dp"
            app:boxStrokeColor="@color/vpn_primary"
            app:hintTextColor="@color/vpn_primary">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/etServerAddress"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="@string/server_address"
                android:inputType="textUri"
                android:maxLines="1" />

        </com.google.android.material.textfield.TextInputLayout>

        <!-- Server Port -->
        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="12dp"
            app:boxStrokeColor="@color/vpn_primary"
            app:hintTextColor="@color/vpn_primary">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/etServerPort"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="@string/server_port"
                android:inputType="number"
                android:maxLines="1" />

        </com.google.android.material.textfield.TextInputLayout>

        <!-- UUID -->
        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="12dp"
            app:boxStrokeColor="@color/vpn_primary"
            app:hintTextColor="@color/vpn_primary">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/etUuid"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="@string/server_uuid"
                android:inputType="text"
                android:maxLines="1" />

        </com.google.android.material.textfield.TextInputLayout>

        <!-- Network Type -->
        <com.google.android.material.textfield.TextInputLayout
            style="@style/Widget.Material3.TextInputLayout.ExposedDropdownMenu"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="12dp"
            app:boxStrokeColor="@color/vpn_primary"
            app:hintTextColor="@color/vpn_primary">

            <AutoCompleteTextView
                android:id="@+id/spinnerNetwork"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="@string/server_network"
                android:inputType="none"
                android:text="TCP" />

        </com.google.android.material.textfield.TextInputLayout>

        <!-- Security Type -->
        <com.google.android.material.textfield.TextInputLayout
            style="@style/Widget.Material3.TextInputLayout.ExposedDropdownMenu"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="12dp"
            app:boxStrokeColor="@color/vpn_primary"
            app:hintTextColor="@color/vpn_primary">

            <AutoCompleteTextView
                android:id="@+id/spinnerSecurity"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="@string/server_security"
                android:inputType="none"
                android:text="None" />

        </com.google.android.material.textfield.TextInputLayout>

        <!-- Advanced Settings Card -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="8dp"
            app:cardElevation="2dp"
            app:cardBackgroundColor="@color/background_secondary">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Advanced Settings"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_primary"
                    android:layout_marginBottom="12dp" />

                <!-- WebSocket Path -->
                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/layoutWsPath"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:visibility="gone"
                    app:boxStrokeColor="@color/vpn_primary"
                    app:hintTextColor="@color/vpn_primary">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/etWsPath"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="@string/server_path"
                        android:inputType="text"
                        android:maxLines="1" />

                </com.google.android.material.textfield.TextInputLayout>

                <!-- Host -->
                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/layoutHost"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:visibility="gone"
                    app:boxStrokeColor="@color/vpn_primary"
                    app:hintTextColor="@color/vpn_primary">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/etHost"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="@string/server_host"
                        android:inputType="text"
                        android:maxLines="1" />

                </com.google.android.material.textfield.TextInputLayout>

                <!-- SNI -->
                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/layoutSni"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:visibility="gone"
                    app:boxStrokeColor="@color/vpn_primary"
                    app:hintTextColor="@color/vpn_primary">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/etSni"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="SNI"
                        android:inputType="text"
                        android:maxLines="1" />

                </com.google.android.material.textfield.TextInputLayout>

                <!-- Allow Insecure -->
                <com.google.android.material.checkbox.MaterialCheckBox
                    android:id="@+id/cbAllowInsecure"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Allow Insecure"
                    android:visibility="gone" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- Buttons -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="end">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnCancel"
                style="@style/Widget.Material3.Button.TextButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="8dp"
                android:text="@string/cancel" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnSave"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/save" />

        </LinearLayout>

    </LinearLayout>

</ScrollView>
