<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="Theme.AndroidVPN" parent="Theme.Material3.DayNight">
        <!-- Primary brand color. -->
        <item name="colorPrimary">@color/vpn_primary</item>
        <item name="colorPrimaryVariant">@color/vpn_primary_dark</item>
        <item name="colorOnPrimary">@color/white</item>
        <!-- Secondary brand color. -->
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_700</item>
        <item name="colorOnSecondary">@color/black</item>
        <!-- Status bar color. -->
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        <!-- Customize your theme here. -->
        <item name="android:windowBackground">@color/background_primary</item>
        <item name="colorSurface">@color/background_card</item>
        <item name="colorOnSurface">@color/text_primary</item>
    </style>
    
    <style name="Theme.AndroidVPN.NoActionBar">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>
    
    <!-- Connection button styles -->
    <style name="ConnectButton" parent="Widget.Material3.Button">
        <item name="android:layout_width">200dp</item>
        <item name="android:layout_height">200dp</item>
        <item name="android:textSize">18sp</item>
        <item name="android:textStyle">bold</item>
        <item name="cornerRadius">100dp</item>
        <item name="android:elevation">8dp</item>
    </style>
    
    <style name="ConnectButton.Connected">
        <item name="backgroundTint">@color/status_connected</item>
    </style>
    
    <style name="ConnectButton.Disconnected">
        <item name="backgroundTint">@color/status_disconnected</item>
    </style>
    
    <style name="ConnectButton.Connecting">
        <item name="backgroundTint">@color/status_connecting</item>
    </style>
    
    <!-- Card styles -->
    <style name="VpnCard" parent="Widget.Material3.CardView">
        <item name="cardCornerRadius">12dp</item>
        <item name="cardElevation">4dp</item>
        <item name="android:layout_margin">8dp</item>
        <item name="cardBackgroundColor">@color/background_card</item>
    </style>
    
    <!-- Text styles -->
    <style name="StatusText">
        <item name="android:textSize">24sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:gravity">center</item>
        <item name="android:layout_marginTop">16dp</item>
    </style>
    
    <style name="StatisticTitle">
        <item name="android:textSize">14sp</item>
        <item name="android:textColor">@color/text_secondary</item>
        <item name="android:textStyle">bold</item>
    </style>
    
    <style name="StatisticValue">
        <item name="android:textSize">18sp</item>
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:textStyle">bold</item>
    </style>
    
    <!-- Server item styles -->
    <style name="ServerName">
        <item name="android:textSize">16sp</item>
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:textStyle">bold</item>
    </style>
    
    <style name="ServerAddress">
        <item name="android:textSize">14sp</item>
        <item name="android:textColor">@color/text_secondary</item>
    </style>
    
    <!-- Toolbar style -->
    <style name="VpnToolbar" parent="Widget.Material3.Toolbar">
        <item name="android:background">@color/vpn_primary</item>
        <item name="titleTextColor">@color/white</item>
        <item name="android:elevation">4dp</item>
    </style>
    
    <!-- Bottom sheet style -->
    <style name="BottomSheetDialog" parent="Theme.Material3.DayNight.BottomSheetDialog">
        <item name="bottomSheetStyle">@style/BottomSheet</item>
    </style>
    
    <style name="BottomSheet" parent="Widget.Material3.BottomSheet">
        <item name="shapeAppearanceOverlay">@style/BottomSheetShape</item>
    </style>
    
    <style name="BottomSheetShape">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSizeTopLeft">16dp</item>
        <item name="cornerSizeTopRight">16dp</item>
    </style>
</resources>
